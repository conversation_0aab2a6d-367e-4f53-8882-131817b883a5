part of 'daily_medication_bloc.dart';

@freezed
class DailyMedicationState with _$DailyMedicationState {
  const factory DailyMedicationState.initial() = _Initial;
  const factory DailyMedicationState.loadInProgress() = _LoadInProgress;
  const factory DailyMedicationState.loadSuccess(List<DailyMedicationModel> medications) = _LoadSuccess;
  const factory DailyMedicationState.loadFailure(MedicationFailure failure) = _LoadFailure;
}

