part of 'medication_form_bloc.dart';

@freezed
class MedicationFormEvent with _$MedicationFormEvent {
  const factory MedicationFormEvent.initialized(Option<MedicationModel> initialMedicationOption) = _Initialized;
  const factory MedicationFormEvent.nameChanged(String nameStr) = _NameChanged;
  const factory MedicationFormEvent.dosageChanged(String dosageStr) = _DosageChanged;
  const factory MedicationFormEvent.frequencyChanged(String frequencyStr) = _FrequencyChanged;
  const factory MedicationFormEvent.frequencyUnitChanged(String frequencyUnitStr) = _FrequencyUnitChanged;
  const factory MedicationFormEvent.dosageUnitChanged(String dosageUnitStr) = _DosageUnitChanged;
  const factory MedicationFormEvent.daystoBeNotifiedChanged(List<String> daystoBeNotified) = _DaystoBeNotifiedChanged;
  const factory MedicationFormEvent.timeofDayChanged(List<String> timeofDay) = _TimeofDayChanged;
  const factory MedicationFormEvent.monthlyDateToBeNotifiedChanged(DateTime monthlyDateToBeNotified) = _MonthlyDateToBeNotifiedChanged;
  const factory MedicationFormEvent.isNotificationEnabledChanged(bool isNotificationEnabled) = _IsNotificationEnabledChanged;
  const factory MedicationFormEvent.notesChanged(String notesStr) = _NotesChanged;
  const factory MedicationFormEvent.startDateChanged(DateTime startDate) = _StartDateChanged;
  // const factory MedicationFormEvent.routeChanged(String routeStr) = _RouteChanged;
  const factory MedicationFormEvent.save() = _Save;

}
