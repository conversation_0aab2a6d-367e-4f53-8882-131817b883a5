// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'medication_form_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$MedicationFormEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Option<MedicationModel> initialMedicationOption)
        initialized,
    required TResult Function(String nameStr) nameChanged,
    required TResult Function(String dosageStr) dosageChanged,
    required TResult Function(String frequencyStr) frequencyChanged,
    required TResult Function(String frequencyUnitStr) frequencyUnitChanged,
    required TResult Function(String dosageUnitStr) dosageUnitChanged,
    required TResult Function(List<String> daystoBeNotified)
        daystoBeNotifiedChanged,
    required TResult Function(List<String> timeofDay) timeofDayChanged,
    required TResult Function(DateTime monthlyDateToBeNotified)
        monthlyDateToBeNotifiedChanged,
    required TResult Function(bool isNotificationEnabled)
        isNotificationEnabledChanged,
    required TResult Function(String notesStr) notesChanged,
    required TResult Function(DateTime startDate) startDateChanged,
    required TResult Function() save,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Option<MedicationModel> initialMedicationOption)?
        initialized,
    TResult? Function(String nameStr)? nameChanged,
    TResult? Function(String dosageStr)? dosageChanged,
    TResult? Function(String frequencyStr)? frequencyChanged,
    TResult? Function(String frequencyUnitStr)? frequencyUnitChanged,
    TResult? Function(String dosageUnitStr)? dosageUnitChanged,
    TResult? Function(List<String> daystoBeNotified)? daystoBeNotifiedChanged,
    TResult? Function(List<String> timeofDay)? timeofDayChanged,
    TResult? Function(DateTime monthlyDateToBeNotified)?
        monthlyDateToBeNotifiedChanged,
    TResult? Function(bool isNotificationEnabled)? isNotificationEnabledChanged,
    TResult? Function(String notesStr)? notesChanged,
    TResult? Function(DateTime startDate)? startDateChanged,
    TResult? Function()? save,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Option<MedicationModel> initialMedicationOption)?
        initialized,
    TResult Function(String nameStr)? nameChanged,
    TResult Function(String dosageStr)? dosageChanged,
    TResult Function(String frequencyStr)? frequencyChanged,
    TResult Function(String frequencyUnitStr)? frequencyUnitChanged,
    TResult Function(String dosageUnitStr)? dosageUnitChanged,
    TResult Function(List<String> daystoBeNotified)? daystoBeNotifiedChanged,
    TResult Function(List<String> timeofDay)? timeofDayChanged,
    TResult Function(DateTime monthlyDateToBeNotified)?
        monthlyDateToBeNotifiedChanged,
    TResult Function(bool isNotificationEnabled)? isNotificationEnabledChanged,
    TResult Function(String notesStr)? notesChanged,
    TResult Function(DateTime startDate)? startDateChanged,
    TResult Function()? save,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialized value) initialized,
    required TResult Function(_NameChanged value) nameChanged,
    required TResult Function(_DosageChanged value) dosageChanged,
    required TResult Function(_FrequencyChanged value) frequencyChanged,
    required TResult Function(_FrequencyUnitChanged value) frequencyUnitChanged,
    required TResult Function(_DosageUnitChanged value) dosageUnitChanged,
    required TResult Function(_DaystoBeNotifiedChanged value)
        daystoBeNotifiedChanged,
    required TResult Function(_TimeofDayChanged value) timeofDayChanged,
    required TResult Function(_MonthlyDateToBeNotifiedChanged value)
        monthlyDateToBeNotifiedChanged,
    required TResult Function(_IsNotificationEnabledChanged value)
        isNotificationEnabledChanged,
    required TResult Function(_NotesChanged value) notesChanged,
    required TResult Function(_StartDateChanged value) startDateChanged,
    required TResult Function(_Save value) save,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialized value)? initialized,
    TResult? Function(_NameChanged value)? nameChanged,
    TResult? Function(_DosageChanged value)? dosageChanged,
    TResult? Function(_FrequencyChanged value)? frequencyChanged,
    TResult? Function(_FrequencyUnitChanged value)? frequencyUnitChanged,
    TResult? Function(_DosageUnitChanged value)? dosageUnitChanged,
    TResult? Function(_DaystoBeNotifiedChanged value)? daystoBeNotifiedChanged,
    TResult? Function(_TimeofDayChanged value)? timeofDayChanged,
    TResult? Function(_MonthlyDateToBeNotifiedChanged value)?
        monthlyDateToBeNotifiedChanged,
    TResult? Function(_IsNotificationEnabledChanged value)?
        isNotificationEnabledChanged,
    TResult? Function(_NotesChanged value)? notesChanged,
    TResult? Function(_StartDateChanged value)? startDateChanged,
    TResult? Function(_Save value)? save,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialized value)? initialized,
    TResult Function(_NameChanged value)? nameChanged,
    TResult Function(_DosageChanged value)? dosageChanged,
    TResult Function(_FrequencyChanged value)? frequencyChanged,
    TResult Function(_FrequencyUnitChanged value)? frequencyUnitChanged,
    TResult Function(_DosageUnitChanged value)? dosageUnitChanged,
    TResult Function(_DaystoBeNotifiedChanged value)? daystoBeNotifiedChanged,
    TResult Function(_TimeofDayChanged value)? timeofDayChanged,
    TResult Function(_MonthlyDateToBeNotifiedChanged value)?
        monthlyDateToBeNotifiedChanged,
    TResult Function(_IsNotificationEnabledChanged value)?
        isNotificationEnabledChanged,
    TResult Function(_NotesChanged value)? notesChanged,
    TResult Function(_StartDateChanged value)? startDateChanged,
    TResult Function(_Save value)? save,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MedicationFormEventCopyWith<$Res> {
  factory $MedicationFormEventCopyWith(
          MedicationFormEvent value, $Res Function(MedicationFormEvent) then) =
      _$MedicationFormEventCopyWithImpl<$Res, MedicationFormEvent>;
}

/// @nodoc
class _$MedicationFormEventCopyWithImpl<$Res, $Val extends MedicationFormEvent>
    implements $MedicationFormEventCopyWith<$Res> {
  _$MedicationFormEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of MedicationFormEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$InitializedImplCopyWith<$Res> {
  factory _$$InitializedImplCopyWith(
          _$InitializedImpl value, $Res Function(_$InitializedImpl) then) =
      __$$InitializedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({Option<MedicationModel> initialMedicationOption});
}

/// @nodoc
class __$$InitializedImplCopyWithImpl<$Res>
    extends _$MedicationFormEventCopyWithImpl<$Res, _$InitializedImpl>
    implements _$$InitializedImplCopyWith<$Res> {
  __$$InitializedImplCopyWithImpl(
      _$InitializedImpl _value, $Res Function(_$InitializedImpl) _then)
      : super(_value, _then);

  /// Create a copy of MedicationFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? initialMedicationOption = null,
  }) {
    return _then(_$InitializedImpl(
      null == initialMedicationOption
          ? _value.initialMedicationOption
          : initialMedicationOption // ignore: cast_nullable_to_non_nullable
              as Option<MedicationModel>,
    ));
  }
}

/// @nodoc

class _$InitializedImpl implements _Initialized {
  const _$InitializedImpl(this.initialMedicationOption);

  @override
  final Option<MedicationModel> initialMedicationOption;

  @override
  String toString() {
    return 'MedicationFormEvent.initialized(initialMedicationOption: $initialMedicationOption)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$InitializedImpl &&
            (identical(
                    other.initialMedicationOption, initialMedicationOption) ||
                other.initialMedicationOption == initialMedicationOption));
  }

  @override
  int get hashCode => Object.hash(runtimeType, initialMedicationOption);

  /// Create a copy of MedicationFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$InitializedImplCopyWith<_$InitializedImpl> get copyWith =>
      __$$InitializedImplCopyWithImpl<_$InitializedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Option<MedicationModel> initialMedicationOption)
        initialized,
    required TResult Function(String nameStr) nameChanged,
    required TResult Function(String dosageStr) dosageChanged,
    required TResult Function(String frequencyStr) frequencyChanged,
    required TResult Function(String frequencyUnitStr) frequencyUnitChanged,
    required TResult Function(String dosageUnitStr) dosageUnitChanged,
    required TResult Function(List<String> daystoBeNotified)
        daystoBeNotifiedChanged,
    required TResult Function(List<String> timeofDay) timeofDayChanged,
    required TResult Function(DateTime monthlyDateToBeNotified)
        monthlyDateToBeNotifiedChanged,
    required TResult Function(bool isNotificationEnabled)
        isNotificationEnabledChanged,
    required TResult Function(String notesStr) notesChanged,
    required TResult Function(DateTime startDate) startDateChanged,
    required TResult Function() save,
  }) {
    return initialized(initialMedicationOption);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Option<MedicationModel> initialMedicationOption)?
        initialized,
    TResult? Function(String nameStr)? nameChanged,
    TResult? Function(String dosageStr)? dosageChanged,
    TResult? Function(String frequencyStr)? frequencyChanged,
    TResult? Function(String frequencyUnitStr)? frequencyUnitChanged,
    TResult? Function(String dosageUnitStr)? dosageUnitChanged,
    TResult? Function(List<String> daystoBeNotified)? daystoBeNotifiedChanged,
    TResult? Function(List<String> timeofDay)? timeofDayChanged,
    TResult? Function(DateTime monthlyDateToBeNotified)?
        monthlyDateToBeNotifiedChanged,
    TResult? Function(bool isNotificationEnabled)? isNotificationEnabledChanged,
    TResult? Function(String notesStr)? notesChanged,
    TResult? Function(DateTime startDate)? startDateChanged,
    TResult? Function()? save,
  }) {
    return initialized?.call(initialMedicationOption);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Option<MedicationModel> initialMedicationOption)?
        initialized,
    TResult Function(String nameStr)? nameChanged,
    TResult Function(String dosageStr)? dosageChanged,
    TResult Function(String frequencyStr)? frequencyChanged,
    TResult Function(String frequencyUnitStr)? frequencyUnitChanged,
    TResult Function(String dosageUnitStr)? dosageUnitChanged,
    TResult Function(List<String> daystoBeNotified)? daystoBeNotifiedChanged,
    TResult Function(List<String> timeofDay)? timeofDayChanged,
    TResult Function(DateTime monthlyDateToBeNotified)?
        monthlyDateToBeNotifiedChanged,
    TResult Function(bool isNotificationEnabled)? isNotificationEnabledChanged,
    TResult Function(String notesStr)? notesChanged,
    TResult Function(DateTime startDate)? startDateChanged,
    TResult Function()? save,
    required TResult orElse(),
  }) {
    if (initialized != null) {
      return initialized(initialMedicationOption);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialized value) initialized,
    required TResult Function(_NameChanged value) nameChanged,
    required TResult Function(_DosageChanged value) dosageChanged,
    required TResult Function(_FrequencyChanged value) frequencyChanged,
    required TResult Function(_FrequencyUnitChanged value) frequencyUnitChanged,
    required TResult Function(_DosageUnitChanged value) dosageUnitChanged,
    required TResult Function(_DaystoBeNotifiedChanged value)
        daystoBeNotifiedChanged,
    required TResult Function(_TimeofDayChanged value) timeofDayChanged,
    required TResult Function(_MonthlyDateToBeNotifiedChanged value)
        monthlyDateToBeNotifiedChanged,
    required TResult Function(_IsNotificationEnabledChanged value)
        isNotificationEnabledChanged,
    required TResult Function(_NotesChanged value) notesChanged,
    required TResult Function(_StartDateChanged value) startDateChanged,
    required TResult Function(_Save value) save,
  }) {
    return initialized(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialized value)? initialized,
    TResult? Function(_NameChanged value)? nameChanged,
    TResult? Function(_DosageChanged value)? dosageChanged,
    TResult? Function(_FrequencyChanged value)? frequencyChanged,
    TResult? Function(_FrequencyUnitChanged value)? frequencyUnitChanged,
    TResult? Function(_DosageUnitChanged value)? dosageUnitChanged,
    TResult? Function(_DaystoBeNotifiedChanged value)? daystoBeNotifiedChanged,
    TResult? Function(_TimeofDayChanged value)? timeofDayChanged,
    TResult? Function(_MonthlyDateToBeNotifiedChanged value)?
        monthlyDateToBeNotifiedChanged,
    TResult? Function(_IsNotificationEnabledChanged value)?
        isNotificationEnabledChanged,
    TResult? Function(_NotesChanged value)? notesChanged,
    TResult? Function(_StartDateChanged value)? startDateChanged,
    TResult? Function(_Save value)? save,
  }) {
    return initialized?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialized value)? initialized,
    TResult Function(_NameChanged value)? nameChanged,
    TResult Function(_DosageChanged value)? dosageChanged,
    TResult Function(_FrequencyChanged value)? frequencyChanged,
    TResult Function(_FrequencyUnitChanged value)? frequencyUnitChanged,
    TResult Function(_DosageUnitChanged value)? dosageUnitChanged,
    TResult Function(_DaystoBeNotifiedChanged value)? daystoBeNotifiedChanged,
    TResult Function(_TimeofDayChanged value)? timeofDayChanged,
    TResult Function(_MonthlyDateToBeNotifiedChanged value)?
        monthlyDateToBeNotifiedChanged,
    TResult Function(_IsNotificationEnabledChanged value)?
        isNotificationEnabledChanged,
    TResult Function(_NotesChanged value)? notesChanged,
    TResult Function(_StartDateChanged value)? startDateChanged,
    TResult Function(_Save value)? save,
    required TResult orElse(),
  }) {
    if (initialized != null) {
      return initialized(this);
    }
    return orElse();
  }
}

abstract class _Initialized implements MedicationFormEvent {
  const factory _Initialized(
          final Option<MedicationModel> initialMedicationOption) =
      _$InitializedImpl;

  Option<MedicationModel> get initialMedicationOption;

  /// Create a copy of MedicationFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$InitializedImplCopyWith<_$InitializedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$NameChangedImplCopyWith<$Res> {
  factory _$$NameChangedImplCopyWith(
          _$NameChangedImpl value, $Res Function(_$NameChangedImpl) then) =
      __$$NameChangedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String nameStr});
}

/// @nodoc
class __$$NameChangedImplCopyWithImpl<$Res>
    extends _$MedicationFormEventCopyWithImpl<$Res, _$NameChangedImpl>
    implements _$$NameChangedImplCopyWith<$Res> {
  __$$NameChangedImplCopyWithImpl(
      _$NameChangedImpl _value, $Res Function(_$NameChangedImpl) _then)
      : super(_value, _then);

  /// Create a copy of MedicationFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? nameStr = null,
  }) {
    return _then(_$NameChangedImpl(
      null == nameStr
          ? _value.nameStr
          : nameStr // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$NameChangedImpl implements _NameChanged {
  const _$NameChangedImpl(this.nameStr);

  @override
  final String nameStr;

  @override
  String toString() {
    return 'MedicationFormEvent.nameChanged(nameStr: $nameStr)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NameChangedImpl &&
            (identical(other.nameStr, nameStr) || other.nameStr == nameStr));
  }

  @override
  int get hashCode => Object.hash(runtimeType, nameStr);

  /// Create a copy of MedicationFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$NameChangedImplCopyWith<_$NameChangedImpl> get copyWith =>
      __$$NameChangedImplCopyWithImpl<_$NameChangedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Option<MedicationModel> initialMedicationOption)
        initialized,
    required TResult Function(String nameStr) nameChanged,
    required TResult Function(String dosageStr) dosageChanged,
    required TResult Function(String frequencyStr) frequencyChanged,
    required TResult Function(String frequencyUnitStr) frequencyUnitChanged,
    required TResult Function(String dosageUnitStr) dosageUnitChanged,
    required TResult Function(List<String> daystoBeNotified)
        daystoBeNotifiedChanged,
    required TResult Function(List<String> timeofDay) timeofDayChanged,
    required TResult Function(DateTime monthlyDateToBeNotified)
        monthlyDateToBeNotifiedChanged,
    required TResult Function(bool isNotificationEnabled)
        isNotificationEnabledChanged,
    required TResult Function(String notesStr) notesChanged,
    required TResult Function(DateTime startDate) startDateChanged,
    required TResult Function() save,
  }) {
    return nameChanged(nameStr);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Option<MedicationModel> initialMedicationOption)?
        initialized,
    TResult? Function(String nameStr)? nameChanged,
    TResult? Function(String dosageStr)? dosageChanged,
    TResult? Function(String frequencyStr)? frequencyChanged,
    TResult? Function(String frequencyUnitStr)? frequencyUnitChanged,
    TResult? Function(String dosageUnitStr)? dosageUnitChanged,
    TResult? Function(List<String> daystoBeNotified)? daystoBeNotifiedChanged,
    TResult? Function(List<String> timeofDay)? timeofDayChanged,
    TResult? Function(DateTime monthlyDateToBeNotified)?
        monthlyDateToBeNotifiedChanged,
    TResult? Function(bool isNotificationEnabled)? isNotificationEnabledChanged,
    TResult? Function(String notesStr)? notesChanged,
    TResult? Function(DateTime startDate)? startDateChanged,
    TResult? Function()? save,
  }) {
    return nameChanged?.call(nameStr);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Option<MedicationModel> initialMedicationOption)?
        initialized,
    TResult Function(String nameStr)? nameChanged,
    TResult Function(String dosageStr)? dosageChanged,
    TResult Function(String frequencyStr)? frequencyChanged,
    TResult Function(String frequencyUnitStr)? frequencyUnitChanged,
    TResult Function(String dosageUnitStr)? dosageUnitChanged,
    TResult Function(List<String> daystoBeNotified)? daystoBeNotifiedChanged,
    TResult Function(List<String> timeofDay)? timeofDayChanged,
    TResult Function(DateTime monthlyDateToBeNotified)?
        monthlyDateToBeNotifiedChanged,
    TResult Function(bool isNotificationEnabled)? isNotificationEnabledChanged,
    TResult Function(String notesStr)? notesChanged,
    TResult Function(DateTime startDate)? startDateChanged,
    TResult Function()? save,
    required TResult orElse(),
  }) {
    if (nameChanged != null) {
      return nameChanged(nameStr);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialized value) initialized,
    required TResult Function(_NameChanged value) nameChanged,
    required TResult Function(_DosageChanged value) dosageChanged,
    required TResult Function(_FrequencyChanged value) frequencyChanged,
    required TResult Function(_FrequencyUnitChanged value) frequencyUnitChanged,
    required TResult Function(_DosageUnitChanged value) dosageUnitChanged,
    required TResult Function(_DaystoBeNotifiedChanged value)
        daystoBeNotifiedChanged,
    required TResult Function(_TimeofDayChanged value) timeofDayChanged,
    required TResult Function(_MonthlyDateToBeNotifiedChanged value)
        monthlyDateToBeNotifiedChanged,
    required TResult Function(_IsNotificationEnabledChanged value)
        isNotificationEnabledChanged,
    required TResult Function(_NotesChanged value) notesChanged,
    required TResult Function(_StartDateChanged value) startDateChanged,
    required TResult Function(_Save value) save,
  }) {
    return nameChanged(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialized value)? initialized,
    TResult? Function(_NameChanged value)? nameChanged,
    TResult? Function(_DosageChanged value)? dosageChanged,
    TResult? Function(_FrequencyChanged value)? frequencyChanged,
    TResult? Function(_FrequencyUnitChanged value)? frequencyUnitChanged,
    TResult? Function(_DosageUnitChanged value)? dosageUnitChanged,
    TResult? Function(_DaystoBeNotifiedChanged value)? daystoBeNotifiedChanged,
    TResult? Function(_TimeofDayChanged value)? timeofDayChanged,
    TResult? Function(_MonthlyDateToBeNotifiedChanged value)?
        monthlyDateToBeNotifiedChanged,
    TResult? Function(_IsNotificationEnabledChanged value)?
        isNotificationEnabledChanged,
    TResult? Function(_NotesChanged value)? notesChanged,
    TResult? Function(_StartDateChanged value)? startDateChanged,
    TResult? Function(_Save value)? save,
  }) {
    return nameChanged?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialized value)? initialized,
    TResult Function(_NameChanged value)? nameChanged,
    TResult Function(_DosageChanged value)? dosageChanged,
    TResult Function(_FrequencyChanged value)? frequencyChanged,
    TResult Function(_FrequencyUnitChanged value)? frequencyUnitChanged,
    TResult Function(_DosageUnitChanged value)? dosageUnitChanged,
    TResult Function(_DaystoBeNotifiedChanged value)? daystoBeNotifiedChanged,
    TResult Function(_TimeofDayChanged value)? timeofDayChanged,
    TResult Function(_MonthlyDateToBeNotifiedChanged value)?
        monthlyDateToBeNotifiedChanged,
    TResult Function(_IsNotificationEnabledChanged value)?
        isNotificationEnabledChanged,
    TResult Function(_NotesChanged value)? notesChanged,
    TResult Function(_StartDateChanged value)? startDateChanged,
    TResult Function(_Save value)? save,
    required TResult orElse(),
  }) {
    if (nameChanged != null) {
      return nameChanged(this);
    }
    return orElse();
  }
}

abstract class _NameChanged implements MedicationFormEvent {
  const factory _NameChanged(final String nameStr) = _$NameChangedImpl;

  String get nameStr;

  /// Create a copy of MedicationFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$NameChangedImplCopyWith<_$NameChangedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$DosageChangedImplCopyWith<$Res> {
  factory _$$DosageChangedImplCopyWith(
          _$DosageChangedImpl value, $Res Function(_$DosageChangedImpl) then) =
      __$$DosageChangedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String dosageStr});
}

/// @nodoc
class __$$DosageChangedImplCopyWithImpl<$Res>
    extends _$MedicationFormEventCopyWithImpl<$Res, _$DosageChangedImpl>
    implements _$$DosageChangedImplCopyWith<$Res> {
  __$$DosageChangedImplCopyWithImpl(
      _$DosageChangedImpl _value, $Res Function(_$DosageChangedImpl) _then)
      : super(_value, _then);

  /// Create a copy of MedicationFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? dosageStr = null,
  }) {
    return _then(_$DosageChangedImpl(
      null == dosageStr
          ? _value.dosageStr
          : dosageStr // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$DosageChangedImpl implements _DosageChanged {
  const _$DosageChangedImpl(this.dosageStr);

  @override
  final String dosageStr;

  @override
  String toString() {
    return 'MedicationFormEvent.dosageChanged(dosageStr: $dosageStr)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DosageChangedImpl &&
            (identical(other.dosageStr, dosageStr) ||
                other.dosageStr == dosageStr));
  }

  @override
  int get hashCode => Object.hash(runtimeType, dosageStr);

  /// Create a copy of MedicationFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DosageChangedImplCopyWith<_$DosageChangedImpl> get copyWith =>
      __$$DosageChangedImplCopyWithImpl<_$DosageChangedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Option<MedicationModel> initialMedicationOption)
        initialized,
    required TResult Function(String nameStr) nameChanged,
    required TResult Function(String dosageStr) dosageChanged,
    required TResult Function(String frequencyStr) frequencyChanged,
    required TResult Function(String frequencyUnitStr) frequencyUnitChanged,
    required TResult Function(String dosageUnitStr) dosageUnitChanged,
    required TResult Function(List<String> daystoBeNotified)
        daystoBeNotifiedChanged,
    required TResult Function(List<String> timeofDay) timeofDayChanged,
    required TResult Function(DateTime monthlyDateToBeNotified)
        monthlyDateToBeNotifiedChanged,
    required TResult Function(bool isNotificationEnabled)
        isNotificationEnabledChanged,
    required TResult Function(String notesStr) notesChanged,
    required TResult Function(DateTime startDate) startDateChanged,
    required TResult Function() save,
  }) {
    return dosageChanged(dosageStr);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Option<MedicationModel> initialMedicationOption)?
        initialized,
    TResult? Function(String nameStr)? nameChanged,
    TResult? Function(String dosageStr)? dosageChanged,
    TResult? Function(String frequencyStr)? frequencyChanged,
    TResult? Function(String frequencyUnitStr)? frequencyUnitChanged,
    TResult? Function(String dosageUnitStr)? dosageUnitChanged,
    TResult? Function(List<String> daystoBeNotified)? daystoBeNotifiedChanged,
    TResult? Function(List<String> timeofDay)? timeofDayChanged,
    TResult? Function(DateTime monthlyDateToBeNotified)?
        monthlyDateToBeNotifiedChanged,
    TResult? Function(bool isNotificationEnabled)? isNotificationEnabledChanged,
    TResult? Function(String notesStr)? notesChanged,
    TResult? Function(DateTime startDate)? startDateChanged,
    TResult? Function()? save,
  }) {
    return dosageChanged?.call(dosageStr);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Option<MedicationModel> initialMedicationOption)?
        initialized,
    TResult Function(String nameStr)? nameChanged,
    TResult Function(String dosageStr)? dosageChanged,
    TResult Function(String frequencyStr)? frequencyChanged,
    TResult Function(String frequencyUnitStr)? frequencyUnitChanged,
    TResult Function(String dosageUnitStr)? dosageUnitChanged,
    TResult Function(List<String> daystoBeNotified)? daystoBeNotifiedChanged,
    TResult Function(List<String> timeofDay)? timeofDayChanged,
    TResult Function(DateTime monthlyDateToBeNotified)?
        monthlyDateToBeNotifiedChanged,
    TResult Function(bool isNotificationEnabled)? isNotificationEnabledChanged,
    TResult Function(String notesStr)? notesChanged,
    TResult Function(DateTime startDate)? startDateChanged,
    TResult Function()? save,
    required TResult orElse(),
  }) {
    if (dosageChanged != null) {
      return dosageChanged(dosageStr);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialized value) initialized,
    required TResult Function(_NameChanged value) nameChanged,
    required TResult Function(_DosageChanged value) dosageChanged,
    required TResult Function(_FrequencyChanged value) frequencyChanged,
    required TResult Function(_FrequencyUnitChanged value) frequencyUnitChanged,
    required TResult Function(_DosageUnitChanged value) dosageUnitChanged,
    required TResult Function(_DaystoBeNotifiedChanged value)
        daystoBeNotifiedChanged,
    required TResult Function(_TimeofDayChanged value) timeofDayChanged,
    required TResult Function(_MonthlyDateToBeNotifiedChanged value)
        monthlyDateToBeNotifiedChanged,
    required TResult Function(_IsNotificationEnabledChanged value)
        isNotificationEnabledChanged,
    required TResult Function(_NotesChanged value) notesChanged,
    required TResult Function(_StartDateChanged value) startDateChanged,
    required TResult Function(_Save value) save,
  }) {
    return dosageChanged(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialized value)? initialized,
    TResult? Function(_NameChanged value)? nameChanged,
    TResult? Function(_DosageChanged value)? dosageChanged,
    TResult? Function(_FrequencyChanged value)? frequencyChanged,
    TResult? Function(_FrequencyUnitChanged value)? frequencyUnitChanged,
    TResult? Function(_DosageUnitChanged value)? dosageUnitChanged,
    TResult? Function(_DaystoBeNotifiedChanged value)? daystoBeNotifiedChanged,
    TResult? Function(_TimeofDayChanged value)? timeofDayChanged,
    TResult? Function(_MonthlyDateToBeNotifiedChanged value)?
        monthlyDateToBeNotifiedChanged,
    TResult? Function(_IsNotificationEnabledChanged value)?
        isNotificationEnabledChanged,
    TResult? Function(_NotesChanged value)? notesChanged,
    TResult? Function(_StartDateChanged value)? startDateChanged,
    TResult? Function(_Save value)? save,
  }) {
    return dosageChanged?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialized value)? initialized,
    TResult Function(_NameChanged value)? nameChanged,
    TResult Function(_DosageChanged value)? dosageChanged,
    TResult Function(_FrequencyChanged value)? frequencyChanged,
    TResult Function(_FrequencyUnitChanged value)? frequencyUnitChanged,
    TResult Function(_DosageUnitChanged value)? dosageUnitChanged,
    TResult Function(_DaystoBeNotifiedChanged value)? daystoBeNotifiedChanged,
    TResult Function(_TimeofDayChanged value)? timeofDayChanged,
    TResult Function(_MonthlyDateToBeNotifiedChanged value)?
        monthlyDateToBeNotifiedChanged,
    TResult Function(_IsNotificationEnabledChanged value)?
        isNotificationEnabledChanged,
    TResult Function(_NotesChanged value)? notesChanged,
    TResult Function(_StartDateChanged value)? startDateChanged,
    TResult Function(_Save value)? save,
    required TResult orElse(),
  }) {
    if (dosageChanged != null) {
      return dosageChanged(this);
    }
    return orElse();
  }
}

abstract class _DosageChanged implements MedicationFormEvent {
  const factory _DosageChanged(final String dosageStr) = _$DosageChangedImpl;

  String get dosageStr;

  /// Create a copy of MedicationFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DosageChangedImplCopyWith<_$DosageChangedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$FrequencyChangedImplCopyWith<$Res> {
  factory _$$FrequencyChangedImplCopyWith(_$FrequencyChangedImpl value,
          $Res Function(_$FrequencyChangedImpl) then) =
      __$$FrequencyChangedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String frequencyStr});
}

/// @nodoc
class __$$FrequencyChangedImplCopyWithImpl<$Res>
    extends _$MedicationFormEventCopyWithImpl<$Res, _$FrequencyChangedImpl>
    implements _$$FrequencyChangedImplCopyWith<$Res> {
  __$$FrequencyChangedImplCopyWithImpl(_$FrequencyChangedImpl _value,
      $Res Function(_$FrequencyChangedImpl) _then)
      : super(_value, _then);

  /// Create a copy of MedicationFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? frequencyStr = null,
  }) {
    return _then(_$FrequencyChangedImpl(
      null == frequencyStr
          ? _value.frequencyStr
          : frequencyStr // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$FrequencyChangedImpl implements _FrequencyChanged {
  const _$FrequencyChangedImpl(this.frequencyStr);

  @override
  final String frequencyStr;

  @override
  String toString() {
    return 'MedicationFormEvent.frequencyChanged(frequencyStr: $frequencyStr)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FrequencyChangedImpl &&
            (identical(other.frequencyStr, frequencyStr) ||
                other.frequencyStr == frequencyStr));
  }

  @override
  int get hashCode => Object.hash(runtimeType, frequencyStr);

  /// Create a copy of MedicationFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FrequencyChangedImplCopyWith<_$FrequencyChangedImpl> get copyWith =>
      __$$FrequencyChangedImplCopyWithImpl<_$FrequencyChangedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Option<MedicationModel> initialMedicationOption)
        initialized,
    required TResult Function(String nameStr) nameChanged,
    required TResult Function(String dosageStr) dosageChanged,
    required TResult Function(String frequencyStr) frequencyChanged,
    required TResult Function(String frequencyUnitStr) frequencyUnitChanged,
    required TResult Function(String dosageUnitStr) dosageUnitChanged,
    required TResult Function(List<String> daystoBeNotified)
        daystoBeNotifiedChanged,
    required TResult Function(List<String> timeofDay) timeofDayChanged,
    required TResult Function(DateTime monthlyDateToBeNotified)
        monthlyDateToBeNotifiedChanged,
    required TResult Function(bool isNotificationEnabled)
        isNotificationEnabledChanged,
    required TResult Function(String notesStr) notesChanged,
    required TResult Function(DateTime startDate) startDateChanged,
    required TResult Function() save,
  }) {
    return frequencyChanged(frequencyStr);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Option<MedicationModel> initialMedicationOption)?
        initialized,
    TResult? Function(String nameStr)? nameChanged,
    TResult? Function(String dosageStr)? dosageChanged,
    TResult? Function(String frequencyStr)? frequencyChanged,
    TResult? Function(String frequencyUnitStr)? frequencyUnitChanged,
    TResult? Function(String dosageUnitStr)? dosageUnitChanged,
    TResult? Function(List<String> daystoBeNotified)? daystoBeNotifiedChanged,
    TResult? Function(List<String> timeofDay)? timeofDayChanged,
    TResult? Function(DateTime monthlyDateToBeNotified)?
        monthlyDateToBeNotifiedChanged,
    TResult? Function(bool isNotificationEnabled)? isNotificationEnabledChanged,
    TResult? Function(String notesStr)? notesChanged,
    TResult? Function(DateTime startDate)? startDateChanged,
    TResult? Function()? save,
  }) {
    return frequencyChanged?.call(frequencyStr);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Option<MedicationModel> initialMedicationOption)?
        initialized,
    TResult Function(String nameStr)? nameChanged,
    TResult Function(String dosageStr)? dosageChanged,
    TResult Function(String frequencyStr)? frequencyChanged,
    TResult Function(String frequencyUnitStr)? frequencyUnitChanged,
    TResult Function(String dosageUnitStr)? dosageUnitChanged,
    TResult Function(List<String> daystoBeNotified)? daystoBeNotifiedChanged,
    TResult Function(List<String> timeofDay)? timeofDayChanged,
    TResult Function(DateTime monthlyDateToBeNotified)?
        monthlyDateToBeNotifiedChanged,
    TResult Function(bool isNotificationEnabled)? isNotificationEnabledChanged,
    TResult Function(String notesStr)? notesChanged,
    TResult Function(DateTime startDate)? startDateChanged,
    TResult Function()? save,
    required TResult orElse(),
  }) {
    if (frequencyChanged != null) {
      return frequencyChanged(frequencyStr);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialized value) initialized,
    required TResult Function(_NameChanged value) nameChanged,
    required TResult Function(_DosageChanged value) dosageChanged,
    required TResult Function(_FrequencyChanged value) frequencyChanged,
    required TResult Function(_FrequencyUnitChanged value) frequencyUnitChanged,
    required TResult Function(_DosageUnitChanged value) dosageUnitChanged,
    required TResult Function(_DaystoBeNotifiedChanged value)
        daystoBeNotifiedChanged,
    required TResult Function(_TimeofDayChanged value) timeofDayChanged,
    required TResult Function(_MonthlyDateToBeNotifiedChanged value)
        monthlyDateToBeNotifiedChanged,
    required TResult Function(_IsNotificationEnabledChanged value)
        isNotificationEnabledChanged,
    required TResult Function(_NotesChanged value) notesChanged,
    required TResult Function(_StartDateChanged value) startDateChanged,
    required TResult Function(_Save value) save,
  }) {
    return frequencyChanged(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialized value)? initialized,
    TResult? Function(_NameChanged value)? nameChanged,
    TResult? Function(_DosageChanged value)? dosageChanged,
    TResult? Function(_FrequencyChanged value)? frequencyChanged,
    TResult? Function(_FrequencyUnitChanged value)? frequencyUnitChanged,
    TResult? Function(_DosageUnitChanged value)? dosageUnitChanged,
    TResult? Function(_DaystoBeNotifiedChanged value)? daystoBeNotifiedChanged,
    TResult? Function(_TimeofDayChanged value)? timeofDayChanged,
    TResult? Function(_MonthlyDateToBeNotifiedChanged value)?
        monthlyDateToBeNotifiedChanged,
    TResult? Function(_IsNotificationEnabledChanged value)?
        isNotificationEnabledChanged,
    TResult? Function(_NotesChanged value)? notesChanged,
    TResult? Function(_StartDateChanged value)? startDateChanged,
    TResult? Function(_Save value)? save,
  }) {
    return frequencyChanged?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialized value)? initialized,
    TResult Function(_NameChanged value)? nameChanged,
    TResult Function(_DosageChanged value)? dosageChanged,
    TResult Function(_FrequencyChanged value)? frequencyChanged,
    TResult Function(_FrequencyUnitChanged value)? frequencyUnitChanged,
    TResult Function(_DosageUnitChanged value)? dosageUnitChanged,
    TResult Function(_DaystoBeNotifiedChanged value)? daystoBeNotifiedChanged,
    TResult Function(_TimeofDayChanged value)? timeofDayChanged,
    TResult Function(_MonthlyDateToBeNotifiedChanged value)?
        monthlyDateToBeNotifiedChanged,
    TResult Function(_IsNotificationEnabledChanged value)?
        isNotificationEnabledChanged,
    TResult Function(_NotesChanged value)? notesChanged,
    TResult Function(_StartDateChanged value)? startDateChanged,
    TResult Function(_Save value)? save,
    required TResult orElse(),
  }) {
    if (frequencyChanged != null) {
      return frequencyChanged(this);
    }
    return orElse();
  }
}

abstract class _FrequencyChanged implements MedicationFormEvent {
  const factory _FrequencyChanged(final String frequencyStr) =
      _$FrequencyChangedImpl;

  String get frequencyStr;

  /// Create a copy of MedicationFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FrequencyChangedImplCopyWith<_$FrequencyChangedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$FrequencyUnitChangedImplCopyWith<$Res> {
  factory _$$FrequencyUnitChangedImplCopyWith(_$FrequencyUnitChangedImpl value,
          $Res Function(_$FrequencyUnitChangedImpl) then) =
      __$$FrequencyUnitChangedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String frequencyUnitStr});
}

/// @nodoc
class __$$FrequencyUnitChangedImplCopyWithImpl<$Res>
    extends _$MedicationFormEventCopyWithImpl<$Res, _$FrequencyUnitChangedImpl>
    implements _$$FrequencyUnitChangedImplCopyWith<$Res> {
  __$$FrequencyUnitChangedImplCopyWithImpl(_$FrequencyUnitChangedImpl _value,
      $Res Function(_$FrequencyUnitChangedImpl) _then)
      : super(_value, _then);

  /// Create a copy of MedicationFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? frequencyUnitStr = null,
  }) {
    return _then(_$FrequencyUnitChangedImpl(
      null == frequencyUnitStr
          ? _value.frequencyUnitStr
          : frequencyUnitStr // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$FrequencyUnitChangedImpl implements _FrequencyUnitChanged {
  const _$FrequencyUnitChangedImpl(this.frequencyUnitStr);

  @override
  final String frequencyUnitStr;

  @override
  String toString() {
    return 'MedicationFormEvent.frequencyUnitChanged(frequencyUnitStr: $frequencyUnitStr)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FrequencyUnitChangedImpl &&
            (identical(other.frequencyUnitStr, frequencyUnitStr) ||
                other.frequencyUnitStr == frequencyUnitStr));
  }

  @override
  int get hashCode => Object.hash(runtimeType, frequencyUnitStr);

  /// Create a copy of MedicationFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FrequencyUnitChangedImplCopyWith<_$FrequencyUnitChangedImpl>
      get copyWith =>
          __$$FrequencyUnitChangedImplCopyWithImpl<_$FrequencyUnitChangedImpl>(
              this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Option<MedicationModel> initialMedicationOption)
        initialized,
    required TResult Function(String nameStr) nameChanged,
    required TResult Function(String dosageStr) dosageChanged,
    required TResult Function(String frequencyStr) frequencyChanged,
    required TResult Function(String frequencyUnitStr) frequencyUnitChanged,
    required TResult Function(String dosageUnitStr) dosageUnitChanged,
    required TResult Function(List<String> daystoBeNotified)
        daystoBeNotifiedChanged,
    required TResult Function(List<String> timeofDay) timeofDayChanged,
    required TResult Function(DateTime monthlyDateToBeNotified)
        monthlyDateToBeNotifiedChanged,
    required TResult Function(bool isNotificationEnabled)
        isNotificationEnabledChanged,
    required TResult Function(String notesStr) notesChanged,
    required TResult Function(DateTime startDate) startDateChanged,
    required TResult Function() save,
  }) {
    return frequencyUnitChanged(frequencyUnitStr);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Option<MedicationModel> initialMedicationOption)?
        initialized,
    TResult? Function(String nameStr)? nameChanged,
    TResult? Function(String dosageStr)? dosageChanged,
    TResult? Function(String frequencyStr)? frequencyChanged,
    TResult? Function(String frequencyUnitStr)? frequencyUnitChanged,
    TResult? Function(String dosageUnitStr)? dosageUnitChanged,
    TResult? Function(List<String> daystoBeNotified)? daystoBeNotifiedChanged,
    TResult? Function(List<String> timeofDay)? timeofDayChanged,
    TResult? Function(DateTime monthlyDateToBeNotified)?
        monthlyDateToBeNotifiedChanged,
    TResult? Function(bool isNotificationEnabled)? isNotificationEnabledChanged,
    TResult? Function(String notesStr)? notesChanged,
    TResult? Function(DateTime startDate)? startDateChanged,
    TResult? Function()? save,
  }) {
    return frequencyUnitChanged?.call(frequencyUnitStr);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Option<MedicationModel> initialMedicationOption)?
        initialized,
    TResult Function(String nameStr)? nameChanged,
    TResult Function(String dosageStr)? dosageChanged,
    TResult Function(String frequencyStr)? frequencyChanged,
    TResult Function(String frequencyUnitStr)? frequencyUnitChanged,
    TResult Function(String dosageUnitStr)? dosageUnitChanged,
    TResult Function(List<String> daystoBeNotified)? daystoBeNotifiedChanged,
    TResult Function(List<String> timeofDay)? timeofDayChanged,
    TResult Function(DateTime monthlyDateToBeNotified)?
        monthlyDateToBeNotifiedChanged,
    TResult Function(bool isNotificationEnabled)? isNotificationEnabledChanged,
    TResult Function(String notesStr)? notesChanged,
    TResult Function(DateTime startDate)? startDateChanged,
    TResult Function()? save,
    required TResult orElse(),
  }) {
    if (frequencyUnitChanged != null) {
      return frequencyUnitChanged(frequencyUnitStr);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialized value) initialized,
    required TResult Function(_NameChanged value) nameChanged,
    required TResult Function(_DosageChanged value) dosageChanged,
    required TResult Function(_FrequencyChanged value) frequencyChanged,
    required TResult Function(_FrequencyUnitChanged value) frequencyUnitChanged,
    required TResult Function(_DosageUnitChanged value) dosageUnitChanged,
    required TResult Function(_DaystoBeNotifiedChanged value)
        daystoBeNotifiedChanged,
    required TResult Function(_TimeofDayChanged value) timeofDayChanged,
    required TResult Function(_MonthlyDateToBeNotifiedChanged value)
        monthlyDateToBeNotifiedChanged,
    required TResult Function(_IsNotificationEnabledChanged value)
        isNotificationEnabledChanged,
    required TResult Function(_NotesChanged value) notesChanged,
    required TResult Function(_StartDateChanged value) startDateChanged,
    required TResult Function(_Save value) save,
  }) {
    return frequencyUnitChanged(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialized value)? initialized,
    TResult? Function(_NameChanged value)? nameChanged,
    TResult? Function(_DosageChanged value)? dosageChanged,
    TResult? Function(_FrequencyChanged value)? frequencyChanged,
    TResult? Function(_FrequencyUnitChanged value)? frequencyUnitChanged,
    TResult? Function(_DosageUnitChanged value)? dosageUnitChanged,
    TResult? Function(_DaystoBeNotifiedChanged value)? daystoBeNotifiedChanged,
    TResult? Function(_TimeofDayChanged value)? timeofDayChanged,
    TResult? Function(_MonthlyDateToBeNotifiedChanged value)?
        monthlyDateToBeNotifiedChanged,
    TResult? Function(_IsNotificationEnabledChanged value)?
        isNotificationEnabledChanged,
    TResult? Function(_NotesChanged value)? notesChanged,
    TResult? Function(_StartDateChanged value)? startDateChanged,
    TResult? Function(_Save value)? save,
  }) {
    return frequencyUnitChanged?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialized value)? initialized,
    TResult Function(_NameChanged value)? nameChanged,
    TResult Function(_DosageChanged value)? dosageChanged,
    TResult Function(_FrequencyChanged value)? frequencyChanged,
    TResult Function(_FrequencyUnitChanged value)? frequencyUnitChanged,
    TResult Function(_DosageUnitChanged value)? dosageUnitChanged,
    TResult Function(_DaystoBeNotifiedChanged value)? daystoBeNotifiedChanged,
    TResult Function(_TimeofDayChanged value)? timeofDayChanged,
    TResult Function(_MonthlyDateToBeNotifiedChanged value)?
        monthlyDateToBeNotifiedChanged,
    TResult Function(_IsNotificationEnabledChanged value)?
        isNotificationEnabledChanged,
    TResult Function(_NotesChanged value)? notesChanged,
    TResult Function(_StartDateChanged value)? startDateChanged,
    TResult Function(_Save value)? save,
    required TResult orElse(),
  }) {
    if (frequencyUnitChanged != null) {
      return frequencyUnitChanged(this);
    }
    return orElse();
  }
}

abstract class _FrequencyUnitChanged implements MedicationFormEvent {
  const factory _FrequencyUnitChanged(final String frequencyUnitStr) =
      _$FrequencyUnitChangedImpl;

  String get frequencyUnitStr;

  /// Create a copy of MedicationFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FrequencyUnitChangedImplCopyWith<_$FrequencyUnitChangedImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$DosageUnitChangedImplCopyWith<$Res> {
  factory _$$DosageUnitChangedImplCopyWith(_$DosageUnitChangedImpl value,
          $Res Function(_$DosageUnitChangedImpl) then) =
      __$$DosageUnitChangedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String dosageUnitStr});
}

/// @nodoc
class __$$DosageUnitChangedImplCopyWithImpl<$Res>
    extends _$MedicationFormEventCopyWithImpl<$Res, _$DosageUnitChangedImpl>
    implements _$$DosageUnitChangedImplCopyWith<$Res> {
  __$$DosageUnitChangedImplCopyWithImpl(_$DosageUnitChangedImpl _value,
      $Res Function(_$DosageUnitChangedImpl) _then)
      : super(_value, _then);

  /// Create a copy of MedicationFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? dosageUnitStr = null,
  }) {
    return _then(_$DosageUnitChangedImpl(
      null == dosageUnitStr
          ? _value.dosageUnitStr
          : dosageUnitStr // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$DosageUnitChangedImpl implements _DosageUnitChanged {
  const _$DosageUnitChangedImpl(this.dosageUnitStr);

  @override
  final String dosageUnitStr;

  @override
  String toString() {
    return 'MedicationFormEvent.dosageUnitChanged(dosageUnitStr: $dosageUnitStr)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DosageUnitChangedImpl &&
            (identical(other.dosageUnitStr, dosageUnitStr) ||
                other.dosageUnitStr == dosageUnitStr));
  }

  @override
  int get hashCode => Object.hash(runtimeType, dosageUnitStr);

  /// Create a copy of MedicationFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DosageUnitChangedImplCopyWith<_$DosageUnitChangedImpl> get copyWith =>
      __$$DosageUnitChangedImplCopyWithImpl<_$DosageUnitChangedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Option<MedicationModel> initialMedicationOption)
        initialized,
    required TResult Function(String nameStr) nameChanged,
    required TResult Function(String dosageStr) dosageChanged,
    required TResult Function(String frequencyStr) frequencyChanged,
    required TResult Function(String frequencyUnitStr) frequencyUnitChanged,
    required TResult Function(String dosageUnitStr) dosageUnitChanged,
    required TResult Function(List<String> daystoBeNotified)
        daystoBeNotifiedChanged,
    required TResult Function(List<String> timeofDay) timeofDayChanged,
    required TResult Function(DateTime monthlyDateToBeNotified)
        monthlyDateToBeNotifiedChanged,
    required TResult Function(bool isNotificationEnabled)
        isNotificationEnabledChanged,
    required TResult Function(String notesStr) notesChanged,
    required TResult Function(DateTime startDate) startDateChanged,
    required TResult Function() save,
  }) {
    return dosageUnitChanged(dosageUnitStr);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Option<MedicationModel> initialMedicationOption)?
        initialized,
    TResult? Function(String nameStr)? nameChanged,
    TResult? Function(String dosageStr)? dosageChanged,
    TResult? Function(String frequencyStr)? frequencyChanged,
    TResult? Function(String frequencyUnitStr)? frequencyUnitChanged,
    TResult? Function(String dosageUnitStr)? dosageUnitChanged,
    TResult? Function(List<String> daystoBeNotified)? daystoBeNotifiedChanged,
    TResult? Function(List<String> timeofDay)? timeofDayChanged,
    TResult? Function(DateTime monthlyDateToBeNotified)?
        monthlyDateToBeNotifiedChanged,
    TResult? Function(bool isNotificationEnabled)? isNotificationEnabledChanged,
    TResult? Function(String notesStr)? notesChanged,
    TResult? Function(DateTime startDate)? startDateChanged,
    TResult? Function()? save,
  }) {
    return dosageUnitChanged?.call(dosageUnitStr);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Option<MedicationModel> initialMedicationOption)?
        initialized,
    TResult Function(String nameStr)? nameChanged,
    TResult Function(String dosageStr)? dosageChanged,
    TResult Function(String frequencyStr)? frequencyChanged,
    TResult Function(String frequencyUnitStr)? frequencyUnitChanged,
    TResult Function(String dosageUnitStr)? dosageUnitChanged,
    TResult Function(List<String> daystoBeNotified)? daystoBeNotifiedChanged,
    TResult Function(List<String> timeofDay)? timeofDayChanged,
    TResult Function(DateTime monthlyDateToBeNotified)?
        monthlyDateToBeNotifiedChanged,
    TResult Function(bool isNotificationEnabled)? isNotificationEnabledChanged,
    TResult Function(String notesStr)? notesChanged,
    TResult Function(DateTime startDate)? startDateChanged,
    TResult Function()? save,
    required TResult orElse(),
  }) {
    if (dosageUnitChanged != null) {
      return dosageUnitChanged(dosageUnitStr);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialized value) initialized,
    required TResult Function(_NameChanged value) nameChanged,
    required TResult Function(_DosageChanged value) dosageChanged,
    required TResult Function(_FrequencyChanged value) frequencyChanged,
    required TResult Function(_FrequencyUnitChanged value) frequencyUnitChanged,
    required TResult Function(_DosageUnitChanged value) dosageUnitChanged,
    required TResult Function(_DaystoBeNotifiedChanged value)
        daystoBeNotifiedChanged,
    required TResult Function(_TimeofDayChanged value) timeofDayChanged,
    required TResult Function(_MonthlyDateToBeNotifiedChanged value)
        monthlyDateToBeNotifiedChanged,
    required TResult Function(_IsNotificationEnabledChanged value)
        isNotificationEnabledChanged,
    required TResult Function(_NotesChanged value) notesChanged,
    required TResult Function(_StartDateChanged value) startDateChanged,
    required TResult Function(_Save value) save,
  }) {
    return dosageUnitChanged(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialized value)? initialized,
    TResult? Function(_NameChanged value)? nameChanged,
    TResult? Function(_DosageChanged value)? dosageChanged,
    TResult? Function(_FrequencyChanged value)? frequencyChanged,
    TResult? Function(_FrequencyUnitChanged value)? frequencyUnitChanged,
    TResult? Function(_DosageUnitChanged value)? dosageUnitChanged,
    TResult? Function(_DaystoBeNotifiedChanged value)? daystoBeNotifiedChanged,
    TResult? Function(_TimeofDayChanged value)? timeofDayChanged,
    TResult? Function(_MonthlyDateToBeNotifiedChanged value)?
        monthlyDateToBeNotifiedChanged,
    TResult? Function(_IsNotificationEnabledChanged value)?
        isNotificationEnabledChanged,
    TResult? Function(_NotesChanged value)? notesChanged,
    TResult? Function(_StartDateChanged value)? startDateChanged,
    TResult? Function(_Save value)? save,
  }) {
    return dosageUnitChanged?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialized value)? initialized,
    TResult Function(_NameChanged value)? nameChanged,
    TResult Function(_DosageChanged value)? dosageChanged,
    TResult Function(_FrequencyChanged value)? frequencyChanged,
    TResult Function(_FrequencyUnitChanged value)? frequencyUnitChanged,
    TResult Function(_DosageUnitChanged value)? dosageUnitChanged,
    TResult Function(_DaystoBeNotifiedChanged value)? daystoBeNotifiedChanged,
    TResult Function(_TimeofDayChanged value)? timeofDayChanged,
    TResult Function(_MonthlyDateToBeNotifiedChanged value)?
        monthlyDateToBeNotifiedChanged,
    TResult Function(_IsNotificationEnabledChanged value)?
        isNotificationEnabledChanged,
    TResult Function(_NotesChanged value)? notesChanged,
    TResult Function(_StartDateChanged value)? startDateChanged,
    TResult Function(_Save value)? save,
    required TResult orElse(),
  }) {
    if (dosageUnitChanged != null) {
      return dosageUnitChanged(this);
    }
    return orElse();
  }
}

abstract class _DosageUnitChanged implements MedicationFormEvent {
  const factory _DosageUnitChanged(final String dosageUnitStr) =
      _$DosageUnitChangedImpl;

  String get dosageUnitStr;

  /// Create a copy of MedicationFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DosageUnitChangedImplCopyWith<_$DosageUnitChangedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$DaystoBeNotifiedChangedImplCopyWith<$Res> {
  factory _$$DaystoBeNotifiedChangedImplCopyWith(
          _$DaystoBeNotifiedChangedImpl value,
          $Res Function(_$DaystoBeNotifiedChangedImpl) then) =
      __$$DaystoBeNotifiedChangedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({List<String> daystoBeNotified});
}

/// @nodoc
class __$$DaystoBeNotifiedChangedImplCopyWithImpl<$Res>
    extends _$MedicationFormEventCopyWithImpl<$Res,
        _$DaystoBeNotifiedChangedImpl>
    implements _$$DaystoBeNotifiedChangedImplCopyWith<$Res> {
  __$$DaystoBeNotifiedChangedImplCopyWithImpl(
      _$DaystoBeNotifiedChangedImpl _value,
      $Res Function(_$DaystoBeNotifiedChangedImpl) _then)
      : super(_value, _then);

  /// Create a copy of MedicationFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? daystoBeNotified = null,
  }) {
    return _then(_$DaystoBeNotifiedChangedImpl(
      null == daystoBeNotified
          ? _value._daystoBeNotified
          : daystoBeNotified // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ));
  }
}

/// @nodoc

class _$DaystoBeNotifiedChangedImpl implements _DaystoBeNotifiedChanged {
  const _$DaystoBeNotifiedChangedImpl(final List<String> daystoBeNotified)
      : _daystoBeNotified = daystoBeNotified;

  final List<String> _daystoBeNotified;
  @override
  List<String> get daystoBeNotified {
    if (_daystoBeNotified is EqualUnmodifiableListView)
      return _daystoBeNotified;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_daystoBeNotified);
  }

  @override
  String toString() {
    return 'MedicationFormEvent.daystoBeNotifiedChanged(daystoBeNotified: $daystoBeNotified)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DaystoBeNotifiedChangedImpl &&
            const DeepCollectionEquality()
                .equals(other._daystoBeNotified, _daystoBeNotified));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_daystoBeNotified));

  /// Create a copy of MedicationFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DaystoBeNotifiedChangedImplCopyWith<_$DaystoBeNotifiedChangedImpl>
      get copyWith => __$$DaystoBeNotifiedChangedImplCopyWithImpl<
          _$DaystoBeNotifiedChangedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Option<MedicationModel> initialMedicationOption)
        initialized,
    required TResult Function(String nameStr) nameChanged,
    required TResult Function(String dosageStr) dosageChanged,
    required TResult Function(String frequencyStr) frequencyChanged,
    required TResult Function(String frequencyUnitStr) frequencyUnitChanged,
    required TResult Function(String dosageUnitStr) dosageUnitChanged,
    required TResult Function(List<String> daystoBeNotified)
        daystoBeNotifiedChanged,
    required TResult Function(List<String> timeofDay) timeofDayChanged,
    required TResult Function(DateTime monthlyDateToBeNotified)
        monthlyDateToBeNotifiedChanged,
    required TResult Function(bool isNotificationEnabled)
        isNotificationEnabledChanged,
    required TResult Function(String notesStr) notesChanged,
    required TResult Function(DateTime startDate) startDateChanged,
    required TResult Function() save,
  }) {
    return daystoBeNotifiedChanged(daystoBeNotified);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Option<MedicationModel> initialMedicationOption)?
        initialized,
    TResult? Function(String nameStr)? nameChanged,
    TResult? Function(String dosageStr)? dosageChanged,
    TResult? Function(String frequencyStr)? frequencyChanged,
    TResult? Function(String frequencyUnitStr)? frequencyUnitChanged,
    TResult? Function(String dosageUnitStr)? dosageUnitChanged,
    TResult? Function(List<String> daystoBeNotified)? daystoBeNotifiedChanged,
    TResult? Function(List<String> timeofDay)? timeofDayChanged,
    TResult? Function(DateTime monthlyDateToBeNotified)?
        monthlyDateToBeNotifiedChanged,
    TResult? Function(bool isNotificationEnabled)? isNotificationEnabledChanged,
    TResult? Function(String notesStr)? notesChanged,
    TResult? Function(DateTime startDate)? startDateChanged,
    TResult? Function()? save,
  }) {
    return daystoBeNotifiedChanged?.call(daystoBeNotified);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Option<MedicationModel> initialMedicationOption)?
        initialized,
    TResult Function(String nameStr)? nameChanged,
    TResult Function(String dosageStr)? dosageChanged,
    TResult Function(String frequencyStr)? frequencyChanged,
    TResult Function(String frequencyUnitStr)? frequencyUnitChanged,
    TResult Function(String dosageUnitStr)? dosageUnitChanged,
    TResult Function(List<String> daystoBeNotified)? daystoBeNotifiedChanged,
    TResult Function(List<String> timeofDay)? timeofDayChanged,
    TResult Function(DateTime monthlyDateToBeNotified)?
        monthlyDateToBeNotifiedChanged,
    TResult Function(bool isNotificationEnabled)? isNotificationEnabledChanged,
    TResult Function(String notesStr)? notesChanged,
    TResult Function(DateTime startDate)? startDateChanged,
    TResult Function()? save,
    required TResult orElse(),
  }) {
    if (daystoBeNotifiedChanged != null) {
      return daystoBeNotifiedChanged(daystoBeNotified);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialized value) initialized,
    required TResult Function(_NameChanged value) nameChanged,
    required TResult Function(_DosageChanged value) dosageChanged,
    required TResult Function(_FrequencyChanged value) frequencyChanged,
    required TResult Function(_FrequencyUnitChanged value) frequencyUnitChanged,
    required TResult Function(_DosageUnitChanged value) dosageUnitChanged,
    required TResult Function(_DaystoBeNotifiedChanged value)
        daystoBeNotifiedChanged,
    required TResult Function(_TimeofDayChanged value) timeofDayChanged,
    required TResult Function(_MonthlyDateToBeNotifiedChanged value)
        monthlyDateToBeNotifiedChanged,
    required TResult Function(_IsNotificationEnabledChanged value)
        isNotificationEnabledChanged,
    required TResult Function(_NotesChanged value) notesChanged,
    required TResult Function(_StartDateChanged value) startDateChanged,
    required TResult Function(_Save value) save,
  }) {
    return daystoBeNotifiedChanged(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialized value)? initialized,
    TResult? Function(_NameChanged value)? nameChanged,
    TResult? Function(_DosageChanged value)? dosageChanged,
    TResult? Function(_FrequencyChanged value)? frequencyChanged,
    TResult? Function(_FrequencyUnitChanged value)? frequencyUnitChanged,
    TResult? Function(_DosageUnitChanged value)? dosageUnitChanged,
    TResult? Function(_DaystoBeNotifiedChanged value)? daystoBeNotifiedChanged,
    TResult? Function(_TimeofDayChanged value)? timeofDayChanged,
    TResult? Function(_MonthlyDateToBeNotifiedChanged value)?
        monthlyDateToBeNotifiedChanged,
    TResult? Function(_IsNotificationEnabledChanged value)?
        isNotificationEnabledChanged,
    TResult? Function(_NotesChanged value)? notesChanged,
    TResult? Function(_StartDateChanged value)? startDateChanged,
    TResult? Function(_Save value)? save,
  }) {
    return daystoBeNotifiedChanged?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialized value)? initialized,
    TResult Function(_NameChanged value)? nameChanged,
    TResult Function(_DosageChanged value)? dosageChanged,
    TResult Function(_FrequencyChanged value)? frequencyChanged,
    TResult Function(_FrequencyUnitChanged value)? frequencyUnitChanged,
    TResult Function(_DosageUnitChanged value)? dosageUnitChanged,
    TResult Function(_DaystoBeNotifiedChanged value)? daystoBeNotifiedChanged,
    TResult Function(_TimeofDayChanged value)? timeofDayChanged,
    TResult Function(_MonthlyDateToBeNotifiedChanged value)?
        monthlyDateToBeNotifiedChanged,
    TResult Function(_IsNotificationEnabledChanged value)?
        isNotificationEnabledChanged,
    TResult Function(_NotesChanged value)? notesChanged,
    TResult Function(_StartDateChanged value)? startDateChanged,
    TResult Function(_Save value)? save,
    required TResult orElse(),
  }) {
    if (daystoBeNotifiedChanged != null) {
      return daystoBeNotifiedChanged(this);
    }
    return orElse();
  }
}

abstract class _DaystoBeNotifiedChanged implements MedicationFormEvent {
  const factory _DaystoBeNotifiedChanged(final List<String> daystoBeNotified) =
      _$DaystoBeNotifiedChangedImpl;

  List<String> get daystoBeNotified;

  /// Create a copy of MedicationFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DaystoBeNotifiedChangedImplCopyWith<_$DaystoBeNotifiedChangedImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$TimeofDayChangedImplCopyWith<$Res> {
  factory _$$TimeofDayChangedImplCopyWith(_$TimeofDayChangedImpl value,
          $Res Function(_$TimeofDayChangedImpl) then) =
      __$$TimeofDayChangedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({List<String> timeofDay});
}

/// @nodoc
class __$$TimeofDayChangedImplCopyWithImpl<$Res>
    extends _$MedicationFormEventCopyWithImpl<$Res, _$TimeofDayChangedImpl>
    implements _$$TimeofDayChangedImplCopyWith<$Res> {
  __$$TimeofDayChangedImplCopyWithImpl(_$TimeofDayChangedImpl _value,
      $Res Function(_$TimeofDayChangedImpl) _then)
      : super(_value, _then);

  /// Create a copy of MedicationFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? timeofDay = null,
  }) {
    return _then(_$TimeofDayChangedImpl(
      null == timeofDay
          ? _value._timeofDay
          : timeofDay // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ));
  }
}

/// @nodoc

class _$TimeofDayChangedImpl implements _TimeofDayChanged {
  const _$TimeofDayChangedImpl(final List<String> timeofDay)
      : _timeofDay = timeofDay;

  final List<String> _timeofDay;
  @override
  List<String> get timeofDay {
    if (_timeofDay is EqualUnmodifiableListView) return _timeofDay;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_timeofDay);
  }

  @override
  String toString() {
    return 'MedicationFormEvent.timeofDayChanged(timeofDay: $timeofDay)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TimeofDayChangedImpl &&
            const DeepCollectionEquality()
                .equals(other._timeofDay, _timeofDay));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_timeofDay));

  /// Create a copy of MedicationFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TimeofDayChangedImplCopyWith<_$TimeofDayChangedImpl> get copyWith =>
      __$$TimeofDayChangedImplCopyWithImpl<_$TimeofDayChangedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Option<MedicationModel> initialMedicationOption)
        initialized,
    required TResult Function(String nameStr) nameChanged,
    required TResult Function(String dosageStr) dosageChanged,
    required TResult Function(String frequencyStr) frequencyChanged,
    required TResult Function(String frequencyUnitStr) frequencyUnitChanged,
    required TResult Function(String dosageUnitStr) dosageUnitChanged,
    required TResult Function(List<String> daystoBeNotified)
        daystoBeNotifiedChanged,
    required TResult Function(List<String> timeofDay) timeofDayChanged,
    required TResult Function(DateTime monthlyDateToBeNotified)
        monthlyDateToBeNotifiedChanged,
    required TResult Function(bool isNotificationEnabled)
        isNotificationEnabledChanged,
    required TResult Function(String notesStr) notesChanged,
    required TResult Function(DateTime startDate) startDateChanged,
    required TResult Function() save,
  }) {
    return timeofDayChanged(timeofDay);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Option<MedicationModel> initialMedicationOption)?
        initialized,
    TResult? Function(String nameStr)? nameChanged,
    TResult? Function(String dosageStr)? dosageChanged,
    TResult? Function(String frequencyStr)? frequencyChanged,
    TResult? Function(String frequencyUnitStr)? frequencyUnitChanged,
    TResult? Function(String dosageUnitStr)? dosageUnitChanged,
    TResult? Function(List<String> daystoBeNotified)? daystoBeNotifiedChanged,
    TResult? Function(List<String> timeofDay)? timeofDayChanged,
    TResult? Function(DateTime monthlyDateToBeNotified)?
        monthlyDateToBeNotifiedChanged,
    TResult? Function(bool isNotificationEnabled)? isNotificationEnabledChanged,
    TResult? Function(String notesStr)? notesChanged,
    TResult? Function(DateTime startDate)? startDateChanged,
    TResult? Function()? save,
  }) {
    return timeofDayChanged?.call(timeofDay);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Option<MedicationModel> initialMedicationOption)?
        initialized,
    TResult Function(String nameStr)? nameChanged,
    TResult Function(String dosageStr)? dosageChanged,
    TResult Function(String frequencyStr)? frequencyChanged,
    TResult Function(String frequencyUnitStr)? frequencyUnitChanged,
    TResult Function(String dosageUnitStr)? dosageUnitChanged,
    TResult Function(List<String> daystoBeNotified)? daystoBeNotifiedChanged,
    TResult Function(List<String> timeofDay)? timeofDayChanged,
    TResult Function(DateTime monthlyDateToBeNotified)?
        monthlyDateToBeNotifiedChanged,
    TResult Function(bool isNotificationEnabled)? isNotificationEnabledChanged,
    TResult Function(String notesStr)? notesChanged,
    TResult Function(DateTime startDate)? startDateChanged,
    TResult Function()? save,
    required TResult orElse(),
  }) {
    if (timeofDayChanged != null) {
      return timeofDayChanged(timeofDay);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialized value) initialized,
    required TResult Function(_NameChanged value) nameChanged,
    required TResult Function(_DosageChanged value) dosageChanged,
    required TResult Function(_FrequencyChanged value) frequencyChanged,
    required TResult Function(_FrequencyUnitChanged value) frequencyUnitChanged,
    required TResult Function(_DosageUnitChanged value) dosageUnitChanged,
    required TResult Function(_DaystoBeNotifiedChanged value)
        daystoBeNotifiedChanged,
    required TResult Function(_TimeofDayChanged value) timeofDayChanged,
    required TResult Function(_MonthlyDateToBeNotifiedChanged value)
        monthlyDateToBeNotifiedChanged,
    required TResult Function(_IsNotificationEnabledChanged value)
        isNotificationEnabledChanged,
    required TResult Function(_NotesChanged value) notesChanged,
    required TResult Function(_StartDateChanged value) startDateChanged,
    required TResult Function(_Save value) save,
  }) {
    return timeofDayChanged(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialized value)? initialized,
    TResult? Function(_NameChanged value)? nameChanged,
    TResult? Function(_DosageChanged value)? dosageChanged,
    TResult? Function(_FrequencyChanged value)? frequencyChanged,
    TResult? Function(_FrequencyUnitChanged value)? frequencyUnitChanged,
    TResult? Function(_DosageUnitChanged value)? dosageUnitChanged,
    TResult? Function(_DaystoBeNotifiedChanged value)? daystoBeNotifiedChanged,
    TResult? Function(_TimeofDayChanged value)? timeofDayChanged,
    TResult? Function(_MonthlyDateToBeNotifiedChanged value)?
        monthlyDateToBeNotifiedChanged,
    TResult? Function(_IsNotificationEnabledChanged value)?
        isNotificationEnabledChanged,
    TResult? Function(_NotesChanged value)? notesChanged,
    TResult? Function(_StartDateChanged value)? startDateChanged,
    TResult? Function(_Save value)? save,
  }) {
    return timeofDayChanged?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialized value)? initialized,
    TResult Function(_NameChanged value)? nameChanged,
    TResult Function(_DosageChanged value)? dosageChanged,
    TResult Function(_FrequencyChanged value)? frequencyChanged,
    TResult Function(_FrequencyUnitChanged value)? frequencyUnitChanged,
    TResult Function(_DosageUnitChanged value)? dosageUnitChanged,
    TResult Function(_DaystoBeNotifiedChanged value)? daystoBeNotifiedChanged,
    TResult Function(_TimeofDayChanged value)? timeofDayChanged,
    TResult Function(_MonthlyDateToBeNotifiedChanged value)?
        monthlyDateToBeNotifiedChanged,
    TResult Function(_IsNotificationEnabledChanged value)?
        isNotificationEnabledChanged,
    TResult Function(_NotesChanged value)? notesChanged,
    TResult Function(_StartDateChanged value)? startDateChanged,
    TResult Function(_Save value)? save,
    required TResult orElse(),
  }) {
    if (timeofDayChanged != null) {
      return timeofDayChanged(this);
    }
    return orElse();
  }
}

abstract class _TimeofDayChanged implements MedicationFormEvent {
  const factory _TimeofDayChanged(final List<String> timeofDay) =
      _$TimeofDayChangedImpl;

  List<String> get timeofDay;

  /// Create a copy of MedicationFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TimeofDayChangedImplCopyWith<_$TimeofDayChangedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$MonthlyDateToBeNotifiedChangedImplCopyWith<$Res> {
  factory _$$MonthlyDateToBeNotifiedChangedImplCopyWith(
          _$MonthlyDateToBeNotifiedChangedImpl value,
          $Res Function(_$MonthlyDateToBeNotifiedChangedImpl) then) =
      __$$MonthlyDateToBeNotifiedChangedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({DateTime monthlyDateToBeNotified});
}

/// @nodoc
class __$$MonthlyDateToBeNotifiedChangedImplCopyWithImpl<$Res>
    extends _$MedicationFormEventCopyWithImpl<$Res,
        _$MonthlyDateToBeNotifiedChangedImpl>
    implements _$$MonthlyDateToBeNotifiedChangedImplCopyWith<$Res> {
  __$$MonthlyDateToBeNotifiedChangedImplCopyWithImpl(
      _$MonthlyDateToBeNotifiedChangedImpl _value,
      $Res Function(_$MonthlyDateToBeNotifiedChangedImpl) _then)
      : super(_value, _then);

  /// Create a copy of MedicationFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? monthlyDateToBeNotified = null,
  }) {
    return _then(_$MonthlyDateToBeNotifiedChangedImpl(
      null == monthlyDateToBeNotified
          ? _value.monthlyDateToBeNotified
          : monthlyDateToBeNotified // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc

class _$MonthlyDateToBeNotifiedChangedImpl
    implements _MonthlyDateToBeNotifiedChanged {
  const _$MonthlyDateToBeNotifiedChangedImpl(this.monthlyDateToBeNotified);

  @override
  final DateTime monthlyDateToBeNotified;

  @override
  String toString() {
    return 'MedicationFormEvent.monthlyDateToBeNotifiedChanged(monthlyDateToBeNotified: $monthlyDateToBeNotified)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MonthlyDateToBeNotifiedChangedImpl &&
            (identical(
                    other.monthlyDateToBeNotified, monthlyDateToBeNotified) ||
                other.monthlyDateToBeNotified == monthlyDateToBeNotified));
  }

  @override
  int get hashCode => Object.hash(runtimeType, monthlyDateToBeNotified);

  /// Create a copy of MedicationFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MonthlyDateToBeNotifiedChangedImplCopyWith<
          _$MonthlyDateToBeNotifiedChangedImpl>
      get copyWith => __$$MonthlyDateToBeNotifiedChangedImplCopyWithImpl<
          _$MonthlyDateToBeNotifiedChangedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Option<MedicationModel> initialMedicationOption)
        initialized,
    required TResult Function(String nameStr) nameChanged,
    required TResult Function(String dosageStr) dosageChanged,
    required TResult Function(String frequencyStr) frequencyChanged,
    required TResult Function(String frequencyUnitStr) frequencyUnitChanged,
    required TResult Function(String dosageUnitStr) dosageUnitChanged,
    required TResult Function(List<String> daystoBeNotified)
        daystoBeNotifiedChanged,
    required TResult Function(List<String> timeofDay) timeofDayChanged,
    required TResult Function(DateTime monthlyDateToBeNotified)
        monthlyDateToBeNotifiedChanged,
    required TResult Function(bool isNotificationEnabled)
        isNotificationEnabledChanged,
    required TResult Function(String notesStr) notesChanged,
    required TResult Function(DateTime startDate) startDateChanged,
    required TResult Function() save,
  }) {
    return monthlyDateToBeNotifiedChanged(monthlyDateToBeNotified);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Option<MedicationModel> initialMedicationOption)?
        initialized,
    TResult? Function(String nameStr)? nameChanged,
    TResult? Function(String dosageStr)? dosageChanged,
    TResult? Function(String frequencyStr)? frequencyChanged,
    TResult? Function(String frequencyUnitStr)? frequencyUnitChanged,
    TResult? Function(String dosageUnitStr)? dosageUnitChanged,
    TResult? Function(List<String> daystoBeNotified)? daystoBeNotifiedChanged,
    TResult? Function(List<String> timeofDay)? timeofDayChanged,
    TResult? Function(DateTime monthlyDateToBeNotified)?
        monthlyDateToBeNotifiedChanged,
    TResult? Function(bool isNotificationEnabled)? isNotificationEnabledChanged,
    TResult? Function(String notesStr)? notesChanged,
    TResult? Function(DateTime startDate)? startDateChanged,
    TResult? Function()? save,
  }) {
    return monthlyDateToBeNotifiedChanged?.call(monthlyDateToBeNotified);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Option<MedicationModel> initialMedicationOption)?
        initialized,
    TResult Function(String nameStr)? nameChanged,
    TResult Function(String dosageStr)? dosageChanged,
    TResult Function(String frequencyStr)? frequencyChanged,
    TResult Function(String frequencyUnitStr)? frequencyUnitChanged,
    TResult Function(String dosageUnitStr)? dosageUnitChanged,
    TResult Function(List<String> daystoBeNotified)? daystoBeNotifiedChanged,
    TResult Function(List<String> timeofDay)? timeofDayChanged,
    TResult Function(DateTime monthlyDateToBeNotified)?
        monthlyDateToBeNotifiedChanged,
    TResult Function(bool isNotificationEnabled)? isNotificationEnabledChanged,
    TResult Function(String notesStr)? notesChanged,
    TResult Function(DateTime startDate)? startDateChanged,
    TResult Function()? save,
    required TResult orElse(),
  }) {
    if (monthlyDateToBeNotifiedChanged != null) {
      return monthlyDateToBeNotifiedChanged(monthlyDateToBeNotified);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialized value) initialized,
    required TResult Function(_NameChanged value) nameChanged,
    required TResult Function(_DosageChanged value) dosageChanged,
    required TResult Function(_FrequencyChanged value) frequencyChanged,
    required TResult Function(_FrequencyUnitChanged value) frequencyUnitChanged,
    required TResult Function(_DosageUnitChanged value) dosageUnitChanged,
    required TResult Function(_DaystoBeNotifiedChanged value)
        daystoBeNotifiedChanged,
    required TResult Function(_TimeofDayChanged value) timeofDayChanged,
    required TResult Function(_MonthlyDateToBeNotifiedChanged value)
        monthlyDateToBeNotifiedChanged,
    required TResult Function(_IsNotificationEnabledChanged value)
        isNotificationEnabledChanged,
    required TResult Function(_NotesChanged value) notesChanged,
    required TResult Function(_StartDateChanged value) startDateChanged,
    required TResult Function(_Save value) save,
  }) {
    return monthlyDateToBeNotifiedChanged(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialized value)? initialized,
    TResult? Function(_NameChanged value)? nameChanged,
    TResult? Function(_DosageChanged value)? dosageChanged,
    TResult? Function(_FrequencyChanged value)? frequencyChanged,
    TResult? Function(_FrequencyUnitChanged value)? frequencyUnitChanged,
    TResult? Function(_DosageUnitChanged value)? dosageUnitChanged,
    TResult? Function(_DaystoBeNotifiedChanged value)? daystoBeNotifiedChanged,
    TResult? Function(_TimeofDayChanged value)? timeofDayChanged,
    TResult? Function(_MonthlyDateToBeNotifiedChanged value)?
        monthlyDateToBeNotifiedChanged,
    TResult? Function(_IsNotificationEnabledChanged value)?
        isNotificationEnabledChanged,
    TResult? Function(_NotesChanged value)? notesChanged,
    TResult? Function(_StartDateChanged value)? startDateChanged,
    TResult? Function(_Save value)? save,
  }) {
    return monthlyDateToBeNotifiedChanged?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialized value)? initialized,
    TResult Function(_NameChanged value)? nameChanged,
    TResult Function(_DosageChanged value)? dosageChanged,
    TResult Function(_FrequencyChanged value)? frequencyChanged,
    TResult Function(_FrequencyUnitChanged value)? frequencyUnitChanged,
    TResult Function(_DosageUnitChanged value)? dosageUnitChanged,
    TResult Function(_DaystoBeNotifiedChanged value)? daystoBeNotifiedChanged,
    TResult Function(_TimeofDayChanged value)? timeofDayChanged,
    TResult Function(_MonthlyDateToBeNotifiedChanged value)?
        monthlyDateToBeNotifiedChanged,
    TResult Function(_IsNotificationEnabledChanged value)?
        isNotificationEnabledChanged,
    TResult Function(_NotesChanged value)? notesChanged,
    TResult Function(_StartDateChanged value)? startDateChanged,
    TResult Function(_Save value)? save,
    required TResult orElse(),
  }) {
    if (monthlyDateToBeNotifiedChanged != null) {
      return monthlyDateToBeNotifiedChanged(this);
    }
    return orElse();
  }
}

abstract class _MonthlyDateToBeNotifiedChanged implements MedicationFormEvent {
  const factory _MonthlyDateToBeNotifiedChanged(
          final DateTime monthlyDateToBeNotified) =
      _$MonthlyDateToBeNotifiedChangedImpl;

  DateTime get monthlyDateToBeNotified;

  /// Create a copy of MedicationFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MonthlyDateToBeNotifiedChangedImplCopyWith<
          _$MonthlyDateToBeNotifiedChangedImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$IsNotificationEnabledChangedImplCopyWith<$Res> {
  factory _$$IsNotificationEnabledChangedImplCopyWith(
          _$IsNotificationEnabledChangedImpl value,
          $Res Function(_$IsNotificationEnabledChangedImpl) then) =
      __$$IsNotificationEnabledChangedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({bool isNotificationEnabled});
}

/// @nodoc
class __$$IsNotificationEnabledChangedImplCopyWithImpl<$Res>
    extends _$MedicationFormEventCopyWithImpl<$Res,
        _$IsNotificationEnabledChangedImpl>
    implements _$$IsNotificationEnabledChangedImplCopyWith<$Res> {
  __$$IsNotificationEnabledChangedImplCopyWithImpl(
      _$IsNotificationEnabledChangedImpl _value,
      $Res Function(_$IsNotificationEnabledChangedImpl) _then)
      : super(_value, _then);

  /// Create a copy of MedicationFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isNotificationEnabled = null,
  }) {
    return _then(_$IsNotificationEnabledChangedImpl(
      null == isNotificationEnabled
          ? _value.isNotificationEnabled
          : isNotificationEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$IsNotificationEnabledChangedImpl
    implements _IsNotificationEnabledChanged {
  const _$IsNotificationEnabledChangedImpl(this.isNotificationEnabled);

  @override
  final bool isNotificationEnabled;

  @override
  String toString() {
    return 'MedicationFormEvent.isNotificationEnabledChanged(isNotificationEnabled: $isNotificationEnabled)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$IsNotificationEnabledChangedImpl &&
            (identical(other.isNotificationEnabled, isNotificationEnabled) ||
                other.isNotificationEnabled == isNotificationEnabled));
  }

  @override
  int get hashCode => Object.hash(runtimeType, isNotificationEnabled);

  /// Create a copy of MedicationFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$IsNotificationEnabledChangedImplCopyWith<
          _$IsNotificationEnabledChangedImpl>
      get copyWith => __$$IsNotificationEnabledChangedImplCopyWithImpl<
          _$IsNotificationEnabledChangedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Option<MedicationModel> initialMedicationOption)
        initialized,
    required TResult Function(String nameStr) nameChanged,
    required TResult Function(String dosageStr) dosageChanged,
    required TResult Function(String frequencyStr) frequencyChanged,
    required TResult Function(String frequencyUnitStr) frequencyUnitChanged,
    required TResult Function(String dosageUnitStr) dosageUnitChanged,
    required TResult Function(List<String> daystoBeNotified)
        daystoBeNotifiedChanged,
    required TResult Function(List<String> timeofDay) timeofDayChanged,
    required TResult Function(DateTime monthlyDateToBeNotified)
        monthlyDateToBeNotifiedChanged,
    required TResult Function(bool isNotificationEnabled)
        isNotificationEnabledChanged,
    required TResult Function(String notesStr) notesChanged,
    required TResult Function(DateTime startDate) startDateChanged,
    required TResult Function() save,
  }) {
    return isNotificationEnabledChanged(isNotificationEnabled);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Option<MedicationModel> initialMedicationOption)?
        initialized,
    TResult? Function(String nameStr)? nameChanged,
    TResult? Function(String dosageStr)? dosageChanged,
    TResult? Function(String frequencyStr)? frequencyChanged,
    TResult? Function(String frequencyUnitStr)? frequencyUnitChanged,
    TResult? Function(String dosageUnitStr)? dosageUnitChanged,
    TResult? Function(List<String> daystoBeNotified)? daystoBeNotifiedChanged,
    TResult? Function(List<String> timeofDay)? timeofDayChanged,
    TResult? Function(DateTime monthlyDateToBeNotified)?
        monthlyDateToBeNotifiedChanged,
    TResult? Function(bool isNotificationEnabled)? isNotificationEnabledChanged,
    TResult? Function(String notesStr)? notesChanged,
    TResult? Function(DateTime startDate)? startDateChanged,
    TResult? Function()? save,
  }) {
    return isNotificationEnabledChanged?.call(isNotificationEnabled);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Option<MedicationModel> initialMedicationOption)?
        initialized,
    TResult Function(String nameStr)? nameChanged,
    TResult Function(String dosageStr)? dosageChanged,
    TResult Function(String frequencyStr)? frequencyChanged,
    TResult Function(String frequencyUnitStr)? frequencyUnitChanged,
    TResult Function(String dosageUnitStr)? dosageUnitChanged,
    TResult Function(List<String> daystoBeNotified)? daystoBeNotifiedChanged,
    TResult Function(List<String> timeofDay)? timeofDayChanged,
    TResult Function(DateTime monthlyDateToBeNotified)?
        monthlyDateToBeNotifiedChanged,
    TResult Function(bool isNotificationEnabled)? isNotificationEnabledChanged,
    TResult Function(String notesStr)? notesChanged,
    TResult Function(DateTime startDate)? startDateChanged,
    TResult Function()? save,
    required TResult orElse(),
  }) {
    if (isNotificationEnabledChanged != null) {
      return isNotificationEnabledChanged(isNotificationEnabled);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialized value) initialized,
    required TResult Function(_NameChanged value) nameChanged,
    required TResult Function(_DosageChanged value) dosageChanged,
    required TResult Function(_FrequencyChanged value) frequencyChanged,
    required TResult Function(_FrequencyUnitChanged value) frequencyUnitChanged,
    required TResult Function(_DosageUnitChanged value) dosageUnitChanged,
    required TResult Function(_DaystoBeNotifiedChanged value)
        daystoBeNotifiedChanged,
    required TResult Function(_TimeofDayChanged value) timeofDayChanged,
    required TResult Function(_MonthlyDateToBeNotifiedChanged value)
        monthlyDateToBeNotifiedChanged,
    required TResult Function(_IsNotificationEnabledChanged value)
        isNotificationEnabledChanged,
    required TResult Function(_NotesChanged value) notesChanged,
    required TResult Function(_StartDateChanged value) startDateChanged,
    required TResult Function(_Save value) save,
  }) {
    return isNotificationEnabledChanged(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialized value)? initialized,
    TResult? Function(_NameChanged value)? nameChanged,
    TResult? Function(_DosageChanged value)? dosageChanged,
    TResult? Function(_FrequencyChanged value)? frequencyChanged,
    TResult? Function(_FrequencyUnitChanged value)? frequencyUnitChanged,
    TResult? Function(_DosageUnitChanged value)? dosageUnitChanged,
    TResult? Function(_DaystoBeNotifiedChanged value)? daystoBeNotifiedChanged,
    TResult? Function(_TimeofDayChanged value)? timeofDayChanged,
    TResult? Function(_MonthlyDateToBeNotifiedChanged value)?
        monthlyDateToBeNotifiedChanged,
    TResult? Function(_IsNotificationEnabledChanged value)?
        isNotificationEnabledChanged,
    TResult? Function(_NotesChanged value)? notesChanged,
    TResult? Function(_StartDateChanged value)? startDateChanged,
    TResult? Function(_Save value)? save,
  }) {
    return isNotificationEnabledChanged?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialized value)? initialized,
    TResult Function(_NameChanged value)? nameChanged,
    TResult Function(_DosageChanged value)? dosageChanged,
    TResult Function(_FrequencyChanged value)? frequencyChanged,
    TResult Function(_FrequencyUnitChanged value)? frequencyUnitChanged,
    TResult Function(_DosageUnitChanged value)? dosageUnitChanged,
    TResult Function(_DaystoBeNotifiedChanged value)? daystoBeNotifiedChanged,
    TResult Function(_TimeofDayChanged value)? timeofDayChanged,
    TResult Function(_MonthlyDateToBeNotifiedChanged value)?
        monthlyDateToBeNotifiedChanged,
    TResult Function(_IsNotificationEnabledChanged value)?
        isNotificationEnabledChanged,
    TResult Function(_NotesChanged value)? notesChanged,
    TResult Function(_StartDateChanged value)? startDateChanged,
    TResult Function(_Save value)? save,
    required TResult orElse(),
  }) {
    if (isNotificationEnabledChanged != null) {
      return isNotificationEnabledChanged(this);
    }
    return orElse();
  }
}

abstract class _IsNotificationEnabledChanged implements MedicationFormEvent {
  const factory _IsNotificationEnabledChanged(
      final bool isNotificationEnabled) = _$IsNotificationEnabledChangedImpl;

  bool get isNotificationEnabled;

  /// Create a copy of MedicationFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$IsNotificationEnabledChangedImplCopyWith<
          _$IsNotificationEnabledChangedImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$NotesChangedImplCopyWith<$Res> {
  factory _$$NotesChangedImplCopyWith(
          _$NotesChangedImpl value, $Res Function(_$NotesChangedImpl) then) =
      __$$NotesChangedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String notesStr});
}

/// @nodoc
class __$$NotesChangedImplCopyWithImpl<$Res>
    extends _$MedicationFormEventCopyWithImpl<$Res, _$NotesChangedImpl>
    implements _$$NotesChangedImplCopyWith<$Res> {
  __$$NotesChangedImplCopyWithImpl(
      _$NotesChangedImpl _value, $Res Function(_$NotesChangedImpl) _then)
      : super(_value, _then);

  /// Create a copy of MedicationFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? notesStr = null,
  }) {
    return _then(_$NotesChangedImpl(
      null == notesStr
          ? _value.notesStr
          : notesStr // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$NotesChangedImpl implements _NotesChanged {
  const _$NotesChangedImpl(this.notesStr);

  @override
  final String notesStr;

  @override
  String toString() {
    return 'MedicationFormEvent.notesChanged(notesStr: $notesStr)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NotesChangedImpl &&
            (identical(other.notesStr, notesStr) ||
                other.notesStr == notesStr));
  }

  @override
  int get hashCode => Object.hash(runtimeType, notesStr);

  /// Create a copy of MedicationFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$NotesChangedImplCopyWith<_$NotesChangedImpl> get copyWith =>
      __$$NotesChangedImplCopyWithImpl<_$NotesChangedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Option<MedicationModel> initialMedicationOption)
        initialized,
    required TResult Function(String nameStr) nameChanged,
    required TResult Function(String dosageStr) dosageChanged,
    required TResult Function(String frequencyStr) frequencyChanged,
    required TResult Function(String frequencyUnitStr) frequencyUnitChanged,
    required TResult Function(String dosageUnitStr) dosageUnitChanged,
    required TResult Function(List<String> daystoBeNotified)
        daystoBeNotifiedChanged,
    required TResult Function(List<String> timeofDay) timeofDayChanged,
    required TResult Function(DateTime monthlyDateToBeNotified)
        monthlyDateToBeNotifiedChanged,
    required TResult Function(bool isNotificationEnabled)
        isNotificationEnabledChanged,
    required TResult Function(String notesStr) notesChanged,
    required TResult Function(DateTime startDate) startDateChanged,
    required TResult Function() save,
  }) {
    return notesChanged(notesStr);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Option<MedicationModel> initialMedicationOption)?
        initialized,
    TResult? Function(String nameStr)? nameChanged,
    TResult? Function(String dosageStr)? dosageChanged,
    TResult? Function(String frequencyStr)? frequencyChanged,
    TResult? Function(String frequencyUnitStr)? frequencyUnitChanged,
    TResult? Function(String dosageUnitStr)? dosageUnitChanged,
    TResult? Function(List<String> daystoBeNotified)? daystoBeNotifiedChanged,
    TResult? Function(List<String> timeofDay)? timeofDayChanged,
    TResult? Function(DateTime monthlyDateToBeNotified)?
        monthlyDateToBeNotifiedChanged,
    TResult? Function(bool isNotificationEnabled)? isNotificationEnabledChanged,
    TResult? Function(String notesStr)? notesChanged,
    TResult? Function(DateTime startDate)? startDateChanged,
    TResult? Function()? save,
  }) {
    return notesChanged?.call(notesStr);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Option<MedicationModel> initialMedicationOption)?
        initialized,
    TResult Function(String nameStr)? nameChanged,
    TResult Function(String dosageStr)? dosageChanged,
    TResult Function(String frequencyStr)? frequencyChanged,
    TResult Function(String frequencyUnitStr)? frequencyUnitChanged,
    TResult Function(String dosageUnitStr)? dosageUnitChanged,
    TResult Function(List<String> daystoBeNotified)? daystoBeNotifiedChanged,
    TResult Function(List<String> timeofDay)? timeofDayChanged,
    TResult Function(DateTime monthlyDateToBeNotified)?
        monthlyDateToBeNotifiedChanged,
    TResult Function(bool isNotificationEnabled)? isNotificationEnabledChanged,
    TResult Function(String notesStr)? notesChanged,
    TResult Function(DateTime startDate)? startDateChanged,
    TResult Function()? save,
    required TResult orElse(),
  }) {
    if (notesChanged != null) {
      return notesChanged(notesStr);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialized value) initialized,
    required TResult Function(_NameChanged value) nameChanged,
    required TResult Function(_DosageChanged value) dosageChanged,
    required TResult Function(_FrequencyChanged value) frequencyChanged,
    required TResult Function(_FrequencyUnitChanged value) frequencyUnitChanged,
    required TResult Function(_DosageUnitChanged value) dosageUnitChanged,
    required TResult Function(_DaystoBeNotifiedChanged value)
        daystoBeNotifiedChanged,
    required TResult Function(_TimeofDayChanged value) timeofDayChanged,
    required TResult Function(_MonthlyDateToBeNotifiedChanged value)
        monthlyDateToBeNotifiedChanged,
    required TResult Function(_IsNotificationEnabledChanged value)
        isNotificationEnabledChanged,
    required TResult Function(_NotesChanged value) notesChanged,
    required TResult Function(_StartDateChanged value) startDateChanged,
    required TResult Function(_Save value) save,
  }) {
    return notesChanged(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialized value)? initialized,
    TResult? Function(_NameChanged value)? nameChanged,
    TResult? Function(_DosageChanged value)? dosageChanged,
    TResult? Function(_FrequencyChanged value)? frequencyChanged,
    TResult? Function(_FrequencyUnitChanged value)? frequencyUnitChanged,
    TResult? Function(_DosageUnitChanged value)? dosageUnitChanged,
    TResult? Function(_DaystoBeNotifiedChanged value)? daystoBeNotifiedChanged,
    TResult? Function(_TimeofDayChanged value)? timeofDayChanged,
    TResult? Function(_MonthlyDateToBeNotifiedChanged value)?
        monthlyDateToBeNotifiedChanged,
    TResult? Function(_IsNotificationEnabledChanged value)?
        isNotificationEnabledChanged,
    TResult? Function(_NotesChanged value)? notesChanged,
    TResult? Function(_StartDateChanged value)? startDateChanged,
    TResult? Function(_Save value)? save,
  }) {
    return notesChanged?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialized value)? initialized,
    TResult Function(_NameChanged value)? nameChanged,
    TResult Function(_DosageChanged value)? dosageChanged,
    TResult Function(_FrequencyChanged value)? frequencyChanged,
    TResult Function(_FrequencyUnitChanged value)? frequencyUnitChanged,
    TResult Function(_DosageUnitChanged value)? dosageUnitChanged,
    TResult Function(_DaystoBeNotifiedChanged value)? daystoBeNotifiedChanged,
    TResult Function(_TimeofDayChanged value)? timeofDayChanged,
    TResult Function(_MonthlyDateToBeNotifiedChanged value)?
        monthlyDateToBeNotifiedChanged,
    TResult Function(_IsNotificationEnabledChanged value)?
        isNotificationEnabledChanged,
    TResult Function(_NotesChanged value)? notesChanged,
    TResult Function(_StartDateChanged value)? startDateChanged,
    TResult Function(_Save value)? save,
    required TResult orElse(),
  }) {
    if (notesChanged != null) {
      return notesChanged(this);
    }
    return orElse();
  }
}

abstract class _NotesChanged implements MedicationFormEvent {
  const factory _NotesChanged(final String notesStr) = _$NotesChangedImpl;

  String get notesStr;

  /// Create a copy of MedicationFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$NotesChangedImplCopyWith<_$NotesChangedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$StartDateChangedImplCopyWith<$Res> {
  factory _$$StartDateChangedImplCopyWith(_$StartDateChangedImpl value,
          $Res Function(_$StartDateChangedImpl) then) =
      __$$StartDateChangedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({DateTime startDate});
}

/// @nodoc
class __$$StartDateChangedImplCopyWithImpl<$Res>
    extends _$MedicationFormEventCopyWithImpl<$Res, _$StartDateChangedImpl>
    implements _$$StartDateChangedImplCopyWith<$Res> {
  __$$StartDateChangedImplCopyWithImpl(_$StartDateChangedImpl _value,
      $Res Function(_$StartDateChangedImpl) _then)
      : super(_value, _then);

  /// Create a copy of MedicationFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? startDate = null,
  }) {
    return _then(_$StartDateChangedImpl(
      null == startDate
          ? _value.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc

class _$StartDateChangedImpl implements _StartDateChanged {
  const _$StartDateChangedImpl(this.startDate);

  @override
  final DateTime startDate;

  @override
  String toString() {
    return 'MedicationFormEvent.startDateChanged(startDate: $startDate)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StartDateChangedImpl &&
            (identical(other.startDate, startDate) ||
                other.startDate == startDate));
  }

  @override
  int get hashCode => Object.hash(runtimeType, startDate);

  /// Create a copy of MedicationFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$StartDateChangedImplCopyWith<_$StartDateChangedImpl> get copyWith =>
      __$$StartDateChangedImplCopyWithImpl<_$StartDateChangedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Option<MedicationModel> initialMedicationOption)
        initialized,
    required TResult Function(String nameStr) nameChanged,
    required TResult Function(String dosageStr) dosageChanged,
    required TResult Function(String frequencyStr) frequencyChanged,
    required TResult Function(String frequencyUnitStr) frequencyUnitChanged,
    required TResult Function(String dosageUnitStr) dosageUnitChanged,
    required TResult Function(List<String> daystoBeNotified)
        daystoBeNotifiedChanged,
    required TResult Function(List<String> timeofDay) timeofDayChanged,
    required TResult Function(DateTime monthlyDateToBeNotified)
        monthlyDateToBeNotifiedChanged,
    required TResult Function(bool isNotificationEnabled)
        isNotificationEnabledChanged,
    required TResult Function(String notesStr) notesChanged,
    required TResult Function(DateTime startDate) startDateChanged,
    required TResult Function() save,
  }) {
    return startDateChanged(startDate);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Option<MedicationModel> initialMedicationOption)?
        initialized,
    TResult? Function(String nameStr)? nameChanged,
    TResult? Function(String dosageStr)? dosageChanged,
    TResult? Function(String frequencyStr)? frequencyChanged,
    TResult? Function(String frequencyUnitStr)? frequencyUnitChanged,
    TResult? Function(String dosageUnitStr)? dosageUnitChanged,
    TResult? Function(List<String> daystoBeNotified)? daystoBeNotifiedChanged,
    TResult? Function(List<String> timeofDay)? timeofDayChanged,
    TResult? Function(DateTime monthlyDateToBeNotified)?
        monthlyDateToBeNotifiedChanged,
    TResult? Function(bool isNotificationEnabled)? isNotificationEnabledChanged,
    TResult? Function(String notesStr)? notesChanged,
    TResult? Function(DateTime startDate)? startDateChanged,
    TResult? Function()? save,
  }) {
    return startDateChanged?.call(startDate);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Option<MedicationModel> initialMedicationOption)?
        initialized,
    TResult Function(String nameStr)? nameChanged,
    TResult Function(String dosageStr)? dosageChanged,
    TResult Function(String frequencyStr)? frequencyChanged,
    TResult Function(String frequencyUnitStr)? frequencyUnitChanged,
    TResult Function(String dosageUnitStr)? dosageUnitChanged,
    TResult Function(List<String> daystoBeNotified)? daystoBeNotifiedChanged,
    TResult Function(List<String> timeofDay)? timeofDayChanged,
    TResult Function(DateTime monthlyDateToBeNotified)?
        monthlyDateToBeNotifiedChanged,
    TResult Function(bool isNotificationEnabled)? isNotificationEnabledChanged,
    TResult Function(String notesStr)? notesChanged,
    TResult Function(DateTime startDate)? startDateChanged,
    TResult Function()? save,
    required TResult orElse(),
  }) {
    if (startDateChanged != null) {
      return startDateChanged(startDate);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialized value) initialized,
    required TResult Function(_NameChanged value) nameChanged,
    required TResult Function(_DosageChanged value) dosageChanged,
    required TResult Function(_FrequencyChanged value) frequencyChanged,
    required TResult Function(_FrequencyUnitChanged value) frequencyUnitChanged,
    required TResult Function(_DosageUnitChanged value) dosageUnitChanged,
    required TResult Function(_DaystoBeNotifiedChanged value)
        daystoBeNotifiedChanged,
    required TResult Function(_TimeofDayChanged value) timeofDayChanged,
    required TResult Function(_MonthlyDateToBeNotifiedChanged value)
        monthlyDateToBeNotifiedChanged,
    required TResult Function(_IsNotificationEnabledChanged value)
        isNotificationEnabledChanged,
    required TResult Function(_NotesChanged value) notesChanged,
    required TResult Function(_StartDateChanged value) startDateChanged,
    required TResult Function(_Save value) save,
  }) {
    return startDateChanged(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialized value)? initialized,
    TResult? Function(_NameChanged value)? nameChanged,
    TResult? Function(_DosageChanged value)? dosageChanged,
    TResult? Function(_FrequencyChanged value)? frequencyChanged,
    TResult? Function(_FrequencyUnitChanged value)? frequencyUnitChanged,
    TResult? Function(_DosageUnitChanged value)? dosageUnitChanged,
    TResult? Function(_DaystoBeNotifiedChanged value)? daystoBeNotifiedChanged,
    TResult? Function(_TimeofDayChanged value)? timeofDayChanged,
    TResult? Function(_MonthlyDateToBeNotifiedChanged value)?
        monthlyDateToBeNotifiedChanged,
    TResult? Function(_IsNotificationEnabledChanged value)?
        isNotificationEnabledChanged,
    TResult? Function(_NotesChanged value)? notesChanged,
    TResult? Function(_StartDateChanged value)? startDateChanged,
    TResult? Function(_Save value)? save,
  }) {
    return startDateChanged?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialized value)? initialized,
    TResult Function(_NameChanged value)? nameChanged,
    TResult Function(_DosageChanged value)? dosageChanged,
    TResult Function(_FrequencyChanged value)? frequencyChanged,
    TResult Function(_FrequencyUnitChanged value)? frequencyUnitChanged,
    TResult Function(_DosageUnitChanged value)? dosageUnitChanged,
    TResult Function(_DaystoBeNotifiedChanged value)? daystoBeNotifiedChanged,
    TResult Function(_TimeofDayChanged value)? timeofDayChanged,
    TResult Function(_MonthlyDateToBeNotifiedChanged value)?
        monthlyDateToBeNotifiedChanged,
    TResult Function(_IsNotificationEnabledChanged value)?
        isNotificationEnabledChanged,
    TResult Function(_NotesChanged value)? notesChanged,
    TResult Function(_StartDateChanged value)? startDateChanged,
    TResult Function(_Save value)? save,
    required TResult orElse(),
  }) {
    if (startDateChanged != null) {
      return startDateChanged(this);
    }
    return orElse();
  }
}

abstract class _StartDateChanged implements MedicationFormEvent {
  const factory _StartDateChanged(final DateTime startDate) =
      _$StartDateChangedImpl;

  DateTime get startDate;

  /// Create a copy of MedicationFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$StartDateChangedImplCopyWith<_$StartDateChangedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SaveImplCopyWith<$Res> {
  factory _$$SaveImplCopyWith(
          _$SaveImpl value, $Res Function(_$SaveImpl) then) =
      __$$SaveImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$SaveImplCopyWithImpl<$Res>
    extends _$MedicationFormEventCopyWithImpl<$Res, _$SaveImpl>
    implements _$$SaveImplCopyWith<$Res> {
  __$$SaveImplCopyWithImpl(_$SaveImpl _value, $Res Function(_$SaveImpl) _then)
      : super(_value, _then);

  /// Create a copy of MedicationFormEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$SaveImpl implements _Save {
  const _$SaveImpl();

  @override
  String toString() {
    return 'MedicationFormEvent.save()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$SaveImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Option<MedicationModel> initialMedicationOption)
        initialized,
    required TResult Function(String nameStr) nameChanged,
    required TResult Function(String dosageStr) dosageChanged,
    required TResult Function(String frequencyStr) frequencyChanged,
    required TResult Function(String frequencyUnitStr) frequencyUnitChanged,
    required TResult Function(String dosageUnitStr) dosageUnitChanged,
    required TResult Function(List<String> daystoBeNotified)
        daystoBeNotifiedChanged,
    required TResult Function(List<String> timeofDay) timeofDayChanged,
    required TResult Function(DateTime monthlyDateToBeNotified)
        monthlyDateToBeNotifiedChanged,
    required TResult Function(bool isNotificationEnabled)
        isNotificationEnabledChanged,
    required TResult Function(String notesStr) notesChanged,
    required TResult Function(DateTime startDate) startDateChanged,
    required TResult Function() save,
  }) {
    return save();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Option<MedicationModel> initialMedicationOption)?
        initialized,
    TResult? Function(String nameStr)? nameChanged,
    TResult? Function(String dosageStr)? dosageChanged,
    TResult? Function(String frequencyStr)? frequencyChanged,
    TResult? Function(String frequencyUnitStr)? frequencyUnitChanged,
    TResult? Function(String dosageUnitStr)? dosageUnitChanged,
    TResult? Function(List<String> daystoBeNotified)? daystoBeNotifiedChanged,
    TResult? Function(List<String> timeofDay)? timeofDayChanged,
    TResult? Function(DateTime monthlyDateToBeNotified)?
        monthlyDateToBeNotifiedChanged,
    TResult? Function(bool isNotificationEnabled)? isNotificationEnabledChanged,
    TResult? Function(String notesStr)? notesChanged,
    TResult? Function(DateTime startDate)? startDateChanged,
    TResult? Function()? save,
  }) {
    return save?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Option<MedicationModel> initialMedicationOption)?
        initialized,
    TResult Function(String nameStr)? nameChanged,
    TResult Function(String dosageStr)? dosageChanged,
    TResult Function(String frequencyStr)? frequencyChanged,
    TResult Function(String frequencyUnitStr)? frequencyUnitChanged,
    TResult Function(String dosageUnitStr)? dosageUnitChanged,
    TResult Function(List<String> daystoBeNotified)? daystoBeNotifiedChanged,
    TResult Function(List<String> timeofDay)? timeofDayChanged,
    TResult Function(DateTime monthlyDateToBeNotified)?
        monthlyDateToBeNotifiedChanged,
    TResult Function(bool isNotificationEnabled)? isNotificationEnabledChanged,
    TResult Function(String notesStr)? notesChanged,
    TResult Function(DateTime startDate)? startDateChanged,
    TResult Function()? save,
    required TResult orElse(),
  }) {
    if (save != null) {
      return save();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialized value) initialized,
    required TResult Function(_NameChanged value) nameChanged,
    required TResult Function(_DosageChanged value) dosageChanged,
    required TResult Function(_FrequencyChanged value) frequencyChanged,
    required TResult Function(_FrequencyUnitChanged value) frequencyUnitChanged,
    required TResult Function(_DosageUnitChanged value) dosageUnitChanged,
    required TResult Function(_DaystoBeNotifiedChanged value)
        daystoBeNotifiedChanged,
    required TResult Function(_TimeofDayChanged value) timeofDayChanged,
    required TResult Function(_MonthlyDateToBeNotifiedChanged value)
        monthlyDateToBeNotifiedChanged,
    required TResult Function(_IsNotificationEnabledChanged value)
        isNotificationEnabledChanged,
    required TResult Function(_NotesChanged value) notesChanged,
    required TResult Function(_StartDateChanged value) startDateChanged,
    required TResult Function(_Save value) save,
  }) {
    return save(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialized value)? initialized,
    TResult? Function(_NameChanged value)? nameChanged,
    TResult? Function(_DosageChanged value)? dosageChanged,
    TResult? Function(_FrequencyChanged value)? frequencyChanged,
    TResult? Function(_FrequencyUnitChanged value)? frequencyUnitChanged,
    TResult? Function(_DosageUnitChanged value)? dosageUnitChanged,
    TResult? Function(_DaystoBeNotifiedChanged value)? daystoBeNotifiedChanged,
    TResult? Function(_TimeofDayChanged value)? timeofDayChanged,
    TResult? Function(_MonthlyDateToBeNotifiedChanged value)?
        monthlyDateToBeNotifiedChanged,
    TResult? Function(_IsNotificationEnabledChanged value)?
        isNotificationEnabledChanged,
    TResult? Function(_NotesChanged value)? notesChanged,
    TResult? Function(_StartDateChanged value)? startDateChanged,
    TResult? Function(_Save value)? save,
  }) {
    return save?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialized value)? initialized,
    TResult Function(_NameChanged value)? nameChanged,
    TResult Function(_DosageChanged value)? dosageChanged,
    TResult Function(_FrequencyChanged value)? frequencyChanged,
    TResult Function(_FrequencyUnitChanged value)? frequencyUnitChanged,
    TResult Function(_DosageUnitChanged value)? dosageUnitChanged,
    TResult Function(_DaystoBeNotifiedChanged value)? daystoBeNotifiedChanged,
    TResult Function(_TimeofDayChanged value)? timeofDayChanged,
    TResult Function(_MonthlyDateToBeNotifiedChanged value)?
        monthlyDateToBeNotifiedChanged,
    TResult Function(_IsNotificationEnabledChanged value)?
        isNotificationEnabledChanged,
    TResult Function(_NotesChanged value)? notesChanged,
    TResult Function(_StartDateChanged value)? startDateChanged,
    TResult Function(_Save value)? save,
    required TResult orElse(),
  }) {
    if (save != null) {
      return save(this);
    }
    return orElse();
  }
}

abstract class _Save implements MedicationFormEvent {
  const factory _Save() = _$SaveImpl;
}

/// @nodoc
mixin _$MedicationFormState {
  MedicationModel get medication => throw _privateConstructorUsedError;
  bool get showErrorMessages => throw _privateConstructorUsedError;
  bool get isEditing => throw _privateConstructorUsedError;
  bool get isSaving => throw _privateConstructorUsedError;
  Option<Either<MedicationFailure, Unit>> get saveFailureOrSuccessOption =>
      throw _privateConstructorUsedError;

  /// Create a copy of MedicationFormState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $MedicationFormStateCopyWith<MedicationFormState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MedicationFormStateCopyWith<$Res> {
  factory $MedicationFormStateCopyWith(
          MedicationFormState value, $Res Function(MedicationFormState) then) =
      _$MedicationFormStateCopyWithImpl<$Res, MedicationFormState>;
  @useResult
  $Res call(
      {MedicationModel medication,
      bool showErrorMessages,
      bool isEditing,
      bool isSaving,
      Option<Either<MedicationFailure, Unit>> saveFailureOrSuccessOption});
}

/// @nodoc
class _$MedicationFormStateCopyWithImpl<$Res, $Val extends MedicationFormState>
    implements $MedicationFormStateCopyWith<$Res> {
  _$MedicationFormStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of MedicationFormState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? medication = null,
    Object? showErrorMessages = null,
    Object? isEditing = null,
    Object? isSaving = null,
    Object? saveFailureOrSuccessOption = null,
  }) {
    return _then(_value.copyWith(
      medication: null == medication
          ? _value.medication
          : medication // ignore: cast_nullable_to_non_nullable
              as MedicationModel,
      showErrorMessages: null == showErrorMessages
          ? _value.showErrorMessages
          : showErrorMessages // ignore: cast_nullable_to_non_nullable
              as bool,
      isEditing: null == isEditing
          ? _value.isEditing
          : isEditing // ignore: cast_nullable_to_non_nullable
              as bool,
      isSaving: null == isSaving
          ? _value.isSaving
          : isSaving // ignore: cast_nullable_to_non_nullable
              as bool,
      saveFailureOrSuccessOption: null == saveFailureOrSuccessOption
          ? _value.saveFailureOrSuccessOption
          : saveFailureOrSuccessOption // ignore: cast_nullable_to_non_nullable
              as Option<Either<MedicationFailure, Unit>>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$MedicationFormStateImplCopyWith<$Res>
    implements $MedicationFormStateCopyWith<$Res> {
  factory _$$MedicationFormStateImplCopyWith(_$MedicationFormStateImpl value,
          $Res Function(_$MedicationFormStateImpl) then) =
      __$$MedicationFormStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {MedicationModel medication,
      bool showErrorMessages,
      bool isEditing,
      bool isSaving,
      Option<Either<MedicationFailure, Unit>> saveFailureOrSuccessOption});
}

/// @nodoc
class __$$MedicationFormStateImplCopyWithImpl<$Res>
    extends _$MedicationFormStateCopyWithImpl<$Res, _$MedicationFormStateImpl>
    implements _$$MedicationFormStateImplCopyWith<$Res> {
  __$$MedicationFormStateImplCopyWithImpl(_$MedicationFormStateImpl _value,
      $Res Function(_$MedicationFormStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of MedicationFormState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? medication = null,
    Object? showErrorMessages = null,
    Object? isEditing = null,
    Object? isSaving = null,
    Object? saveFailureOrSuccessOption = null,
  }) {
    return _then(_$MedicationFormStateImpl(
      medication: null == medication
          ? _value.medication
          : medication // ignore: cast_nullable_to_non_nullable
              as MedicationModel,
      showErrorMessages: null == showErrorMessages
          ? _value.showErrorMessages
          : showErrorMessages // ignore: cast_nullable_to_non_nullable
              as bool,
      isEditing: null == isEditing
          ? _value.isEditing
          : isEditing // ignore: cast_nullable_to_non_nullable
              as bool,
      isSaving: null == isSaving
          ? _value.isSaving
          : isSaving // ignore: cast_nullable_to_non_nullable
              as bool,
      saveFailureOrSuccessOption: null == saveFailureOrSuccessOption
          ? _value.saveFailureOrSuccessOption
          : saveFailureOrSuccessOption // ignore: cast_nullable_to_non_nullable
              as Option<Either<MedicationFailure, Unit>>,
    ));
  }
}

/// @nodoc

class _$MedicationFormStateImpl implements _MedicationFormState {
  const _$MedicationFormStateImpl(
      {required this.medication,
      required this.showErrorMessages,
      required this.isEditing,
      required this.isSaving,
      required this.saveFailureOrSuccessOption});

  @override
  final MedicationModel medication;
  @override
  final bool showErrorMessages;
  @override
  final bool isEditing;
  @override
  final bool isSaving;
  @override
  final Option<Either<MedicationFailure, Unit>> saveFailureOrSuccessOption;

  @override
  String toString() {
    return 'MedicationFormState(medication: $medication, showErrorMessages: $showErrorMessages, isEditing: $isEditing, isSaving: $isSaving, saveFailureOrSuccessOption: $saveFailureOrSuccessOption)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MedicationFormStateImpl &&
            (identical(other.medication, medication) ||
                other.medication == medication) &&
            (identical(other.showErrorMessages, showErrorMessages) ||
                other.showErrorMessages == showErrorMessages) &&
            (identical(other.isEditing, isEditing) ||
                other.isEditing == isEditing) &&
            (identical(other.isSaving, isSaving) ||
                other.isSaving == isSaving) &&
            (identical(other.saveFailureOrSuccessOption,
                    saveFailureOrSuccessOption) ||
                other.saveFailureOrSuccessOption ==
                    saveFailureOrSuccessOption));
  }

  @override
  int get hashCode => Object.hash(runtimeType, medication, showErrorMessages,
      isEditing, isSaving, saveFailureOrSuccessOption);

  /// Create a copy of MedicationFormState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MedicationFormStateImplCopyWith<_$MedicationFormStateImpl> get copyWith =>
      __$$MedicationFormStateImplCopyWithImpl<_$MedicationFormStateImpl>(
          this, _$identity);
}

abstract class _MedicationFormState implements MedicationFormState {
  const factory _MedicationFormState(
      {required final MedicationModel medication,
      required final bool showErrorMessages,
      required final bool isEditing,
      required final bool isSaving,
      required final Option<Either<MedicationFailure, Unit>>
          saveFailureOrSuccessOption}) = _$MedicationFormStateImpl;

  @override
  MedicationModel get medication;
  @override
  bool get showErrorMessages;
  @override
  bool get isEditing;
  @override
  bool get isSaving;
  @override
  Option<Either<MedicationFailure, Unit>> get saveFailureOrSuccessOption;

  /// Create a copy of MedicationFormState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MedicationFormStateImplCopyWith<_$MedicationFormStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
