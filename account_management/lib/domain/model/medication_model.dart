import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:uuid/uuid.dart';

part 'medication_model.g.dart';

@JsonSerializable(explicitToJson: true)
class MedicationModel extends Equatable {
  final String? id;
  final String? name;
  final String? dosage;
  final String? frequency;
  final String? frequencyUnit;
  final String? dosageUnit;
  final List<String> daystoBeNotified;
  final List<String> timeofDay;
  final DateTime? monthlyDateToBeNotified;
  final bool? isNotificationEnabled;
  final String? notes;
  final String? userId;
  final List<DateTime> loggedTimes;
  final DateTime? startDate;

  const MedicationModel({
    this.id,
    this.name,
    this.dosage,
    this.frequency,
    this.frequencyUnit,
    this.dosageUnit,
    this.daystoBeNotified = const [],
    this.timeofDay = const [],
    this.isNotificationEnabled,
    this.notes,
    this.userId,
    this.monthlyDateToBeNotified,
    this.loggedTimes = const [],
    this.startDate,

  });

  factory MedicationModel.empty() => MedicationModel(
    id: const Uuid().v1(),
    name: '',
    dosage: '10', // Default dosage
    frequency: '1', // Default frequency
    frequencyUnit: 'daily', // Default frequency unit
    dosageUnit: 'mg', // Default dosage unit
    daystoBeNotified: const [],
    timeofDay: const [],
    isNotificationEnabled: false,
    notes: '',
    monthlyDateToBeNotified: DateTime.now(),
    userId: '',
    loggedTimes: const [],
    startDate: DateTime.now(),
  );

  /// `copyWith` method for immutability.
  MedicationModel copyWith({
    String? id,
    String? name,
    String? dosage,
    String? frequency,
    String? frequencyUnit,
    String? dosageUnit,
    List<String>? daystoBeNotified,
    List<String>? timeofDay,
    DateTime? monthlyDateToBeNotified,
    bool? isNotificationEnabled,
    String? notes,
    String? userId,
    List<DateTime>? loggedTimes,
    DateTime? startDate,
  }) {
    return MedicationModel(
      id: id ?? this.id,
      name: name ?? this.name,
      dosage: dosage ?? this.dosage,
      frequency: frequency ?? this.frequency,
      frequencyUnit: frequencyUnit ?? this.frequencyUnit,
      dosageUnit: dosageUnit ?? this.dosageUnit,
      daystoBeNotified: daystoBeNotified ?? this.daystoBeNotified,
      timeofDay: timeofDay ?? this.timeofDay,
      monthlyDateToBeNotified:
      monthlyDateToBeNotified ?? this.monthlyDateToBeNotified,
      isNotificationEnabled:
      isNotificationEnabled ?? this.isNotificationEnabled,
      notes: notes ?? this.notes,
      userId: userId ?? this.userId,
      loggedTimes: loggedTimes ?? this.loggedTimes,
      startDate: startDate ?? this.startDate,
    );
  }

  @override
  List<Object?> get props => [
    id,
    name,
    dosage,
    frequency,
    frequencyUnit,
    dosageUnit,
    daystoBeNotified,
    timeofDay,
    monthlyDateToBeNotified,
    isNotificationEnabled,
    notes,
    userId,
    loggedTimes,
    startDate,
  ];

  Map<String, dynamic> toJson() => _$MedicationModelToJson(this);
  factory MedicationModel.fromJson(Map<String, dynamic> json) =>
      _$MedicationModelFromJson(json);
}
