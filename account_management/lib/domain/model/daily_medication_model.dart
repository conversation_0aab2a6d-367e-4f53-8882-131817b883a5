import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:json_annotation/json_annotation.dart';

part 'daily_medication_model.g.dart';

@JsonSerializable(explicitToJson: true)
class DailyMedicationModel extends Equatable {
  final String? medName;

  @Json<PERSON>ey(
    fromJson: _timeOfDayFromJson,
    toJson: _timeOfDayToJson,
  )
  final TimeOfDay? time;
  final String? medDosage;
  final String? medDosageUnit;
  final String? medNotes;

  @JsonKey(
    fromJson: _timeOfDayFromJson,
    toJson: _timeOfDayToJson,
  )
  TimeOfDay? loggedTime;

  DailyMedicationModel({
    this.medName,
    this.time,
    this.medDosage,
    this.medDosageUnit,
    this.medNotes,
    this.loggedTime,
  });

  factory DailyMedicationModel.empty() => DailyMedicationModel(
    medName: '',
    time: TimeOfDay.now(),
    medDosage: '10',
    medDosageUnit: 'mg',
    medNotes: '',
    loggedTime: null,
  );

  /// `copyWith` method for immutability.
  DailyMedicationModel copyWith({
    String? medName,
    TimeOfDay? time,
    String? medDosage,
    String? medDosageUnit,
    String? medNotes,
    TimeOfDay? loggedTime,
  }) {
    return DailyMedicationModel(
      medName: medName ?? this.medName,
      time: time ?? this.time,
      medDosage: medDosage ?? this.medDosage,
      medDosageUnit: medDosageUnit ?? this.medDosageUnit,
      medNotes: medNotes ?? this.medNotes,
      loggedTime: loggedTime ?? this.loggedTime,
    );
  }

  @override
  List<Object?> get props => [
    medName,
    time,
    medDosage,
    medDosageUnit,
    medNotes,
    loggedTime,
  ];

  Map<String, dynamic> toJson() => _$DailyMedicationModelToJson(this);

  factory DailyMedicationModel.fromJson(Map<String, dynamic> json) =>
      _$DailyMedicationModelFromJson(json);

  // Helper function to convert TimeOfDay to String (HH:mm)
  static String? _timeOfDayToJson(TimeOfDay? time) =>
      time != null ? '${time.hour}:${time.minute}' : null;

  // Helper function to convert String (HH:mm) to TimeOfDay
  static TimeOfDay? _timeOfDayFromJson(String? time) {
    if (time == null) return null;
    final parts = time.split(':');
    return TimeOfDay(
      hour: int.parse(parts[0]),
      minute: int.parse(parts[1]),
    );
  }
}
