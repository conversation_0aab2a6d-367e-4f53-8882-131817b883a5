import 'package:fpdart/fpdart.dart';
import '../failure/medication_failure.dart';
import '../model/daily_medication_model.dart';
import '../model/medication_model.dart';


abstract class MedicationFacade {
  Future<Either<MedicationFailure, Unit>> create(MedicationModel medication);
  Future<Either<MedicationFailure, Unit>> update(MedicationModel medication);
  Future<Either<MedicationFailure, Unit>> delete(MedicationModel medication);
  Stream<Either<MedicationFailure, List<MedicationModel>>> getMedications();
  Stream<Either<MedicationFailure, List<DailyMedicationModel>>> getDailyMedications();

}