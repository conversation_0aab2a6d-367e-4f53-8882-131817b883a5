import 'dart:async';
import 'dart:convert';

import 'package:account_management/account_management.dart';
import 'package:account_management/domain/model/health_data.dart';
import 'package:account_management/repository/menstrual_cycle_data_model.dart';
import 'package:account_management/repository/period_tracking_data_model.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:fpdart/fpdart.dart';
import 'package:injectable/injectable.dart';
import 'package:rxdart/rxdart.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../domain/facade/period_tracking_facade.dart';
import '../domain/failure/period_tracking_failure.dart';
import '../domain/model/period_tracking_model.dart';
import '../domain/model/period_reminder_settings.dart';
import 'package:notifications/domain/facade/scheduled_notifications_facade.dart';
import 'package:timezone/timezone.dart' as tz;

@LazySingleton(as: PeriodTrackingFacade)
class PeriodTrackingRepository implements PeriodTrackingFacade {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _firebaseAuth = FirebaseAuth.instance;
  final ScheduledNotificationsFacade _notificationsFacade;

  PeriodTrackingRepository(
      @Named("externalNotificationsFacade") this._notificationsFacade);
  PeriodTrackingModel _selectedPeriodTrackingDay = PeriodTrackingModel.empty();
  List<PeriodTrackingModel> periodTrackingList = [];
  DateTime _focusedDay = DateTime.now();
  final Set<DateTime> _selectedDays = {};
  Set<DateTime> _ovulationDates = {};
  DateTime _lastPeriodDate = DateTime.now();
  int _cycleLength = 28;
  int _periodLength = 6;

  // Local storage for pending changes (not yet saved to Firestore)
  final Set<DateTime> _pendingSelectedDays = {};
  final Set<DateTime> _pendingRemovedDays = {};

  // Flag to prevent multiple future predictions calculations
  bool _futurePredictionsAdded = false;

  // Cache for loaded data to avoid repeated Firestore reads
  final Map<String, Map<String, dynamic>> _cachedMonthlyData = {};

  // Local storage keys
  static const String _selectedDaysKey = 'period_selected_days';
  static const String _lastPeriodDateKey = 'period_last_period_date';
  static const String _cycleLengthKey = 'period_cycle_length';
  static const String _periodLengthKey = 'period_period_length';
  static const String _ovulationDatesKey = 'period_ovulation_dates';
  static const String _lastSyncKey = 'period_last_sync';
  static const String _reminderSettingsKey = 'period_reminder_settings';

  final BehaviorSubject<Set<DateTime>> _selectedDaysController =
      BehaviorSubject.seeded({});
  final BehaviorSubject<DateTime> _focusedDayController =
      BehaviorSubject.seeded(DateTime.now());
  final BehaviorSubject<List<PeriodTrackingModel>>
      _periodTrackingListController = BehaviorSubject.seeded([]);
  final BehaviorSubject<PeriodTrackingModel>
      _selectedPeriodTrackingDayController =
      BehaviorSubject.seeded(PeriodTrackingModel.empty());
  final BehaviorSubject<Set<DateTime>> _ovulationDatesController =
      BehaviorSubject.seeded({});
  final BehaviorSubject<DateTime> _lastPeriodDateController =
      BehaviorSubject.seeded(DateTime.now());

  // Getters for streams
  Stream<Set<DateTime>> get selectedDaysStream =>
      _selectedDaysController.stream;
  Stream<DateTime> get focusedDayStream => _focusedDayController.stream;
  Stream<List<PeriodTrackingModel>> get periodTrackingListStream =>
      _periodTrackingListController.stream;
  Stream<PeriodTrackingModel> get selectedPeriodTrackingDayStream =>
      _selectedPeriodTrackingDayController.stream;
  Stream<Set<DateTime>> get ovulationDatesStream =>
      _ovulationDatesController.stream;
  Stream<DateTime> get lastPeriodDateStream => _lastPeriodDateController.stream;

  Future<void> fetchAndCalculatePeriodDates() async {
    try {
      print('🔄 Loading period tracking data fresh from Firestore...');

      // Clear all local data first to ensure consistency
      _selectedDays.clear();
      _ovulationDates.clear();
      _pendingSelectedDays.clear();
      _pendingRemovedDays.clear();
      _futurePredictionsAdded = false;

      print('🧹 Cleared all local data for fresh start');

      final user = _firebaseAuth.currentUser;
      if (user == null) return;

      final userDoc = await _firestore.collection('users').doc(user.uid).get();
      if (!userDoc.exists) return;

      HealthDataModel healthData =
          HealthDataModel.fromJson(userDoc.data()!['healthData']);

      final lastPeriodDate = healthData.lastPeriodDate;
      final cloudCycleLength = healthData.cycleLength ?? 28;
      final cloudPeriodLength = healthData.periodLength ?? 6;

      // Use cloud data
      if (lastPeriodDate != null) {
        _lastPeriodDate = lastPeriodDate;
        _cycleLength = cloudCycleLength;
        _periodLength = cloudPeriodLength;
        _lastPeriodDateController.add(_lastPeriodDate);
      }

      // Load fresh data from Firestore only
      print('Loading fresh data from Firestore...');
      await _loadPeriodDataFromFirestore();
      print('After Firestore load: ${_selectedDays.length} selected days');

      // Calculate ovulation based on existing data
      calculateOvulationDays();

      // Save to local storage as temporary cache for this session only
      await _saveToLocalStorage();

      // Update streams with loaded data
      _selectedDaysController.add(_selectedDays);
      _ovulationDatesController.add(_ovulationDates);
    } on FirebaseException catch (e) {
      // Handle Firebase-specific exceptions
      print('FirebaseException: ${e.message}');
    } catch (e) {
      // Handle any other exceptions
      print('Exception: $e');
    }
  }

  //function to calculate the ovulation dates

  // Retrieves a stream of period tracking data for the current user.
  Future<Either<PeriodTrackingFailure, Unit>> getPeriodTrackingDetails() async {
    try {
      print("getPeriodTrackingDetails");
      final String userId = _firebaseAuth.currentUser!.uid;

      // Always reload data to ensure we get the latest saved period dates
      // Remove the caching check so data is always fresh
      // if (_isDataLoaded && _pendingSelectedDays.isEmpty && _pendingRemovedDays.isEmpty) {
      //   print("Using cached data");
      //   return Right(unit);
      // }

      // Get user's date of birth to determine the earliest possible period tracking date
      final userDoc = await _firestore.collection('users').doc(userId).get();
      if (!userDoc.exists) {
        return Left(PeriodTrackingFailure.notFound());
      }

      final userData = userDoc.data();
      DateTime? userDateOfBirth;
      if (userData != null && userData.containsKey('dateOfBirth')) {
        final dobTimestamp = userData['dateOfBirth'] as Timestamp?;
        userDateOfBirth = dobTimestamp?.toDate();
      }

      // Calculate the earliest period tracking date (typically around 10-16 years old)
      DateTime earliestPeriodDate = userDateOfBirth != null
          ? userDateOfBirth
              .add(const Duration(days: 365 * 10)) // Start from 10 years old
          : DateTime.now().subtract(
              const Duration(days: 365 * 20)); // Fallback to 20 years ago

      final now = DateTime.now();
      final futureDate =
          now.add(const Duration(days: 365 * 2)); // 2 years in the future

      // Don't reset focused day here to avoid jumping back to today
      // Store the current focused day to restore it later
      DateTime preservedFocusedDay = _focusedDay;

      // Load fresh data from Firestore (no local storage merge)
      await fetchAndCalculatePeriodDates();

      // Get past 12 months and future 12 months of data for better history
      List<String> monthsToFetch = [];
      DateTime startMonth = DateTime(now.year, now.month - 12, 1);
      DateTime endMonth = DateTime(futureDate.year, futureDate.month, 1);

      DateTime currentMonth = startMonth;
      while (currentMonth.isBefore(endMonth) ||
          currentMonth.isAtSameMomentAs(endMonth)) {
        monthsToFetch.add('${currentMonth.year}_${currentMonth.month}');
        currentMonth = DateTime(currentMonth.year, currentMonth.month + 1, 1);
      }

      List<PeriodTrackingModel> periodTrackingDetails = [];

      // Extract period tracking data from cached monthly data (already loaded by fetchAndCalculatePeriodDates)
      for (String monthKey in monthsToFetch) {
        if (_cachedMonthlyData.containsKey(monthKey)) {
          final monthData = _cachedMonthlyData[monthKey];
          if (monthData != null) {
            // Extract period tracking data
            periodTrackingDetails.addAll(
              _extractPeriodTrackingData(
                  monthData, futureDate, earliestPeriodDate),
            );
          }
        }
      }

      print('Loaded ${_selectedDays.length} period dates from Firestore');

      // Apply pending changes to the selected days
      print('🔄 Adding ${_pendingSelectedDays.length} pending selected days');
      _selectedDays.addAll(_pendingSelectedDays);
      for (final removedDay in _pendingRemovedDays) {
        _selectedDays.remove(removedDay);
      }

      // Add future predictions after loading saved data
      await _addFuturePredictions();

      print(
          "Loaded period dates from Firestore: ${_selectedDays.where((date) => !isDateInFuture(date)).toList()}");
      print(
          "Total selected days (including predictions): ${_selectedDays.length}");

      // Sort by date in descending order
      periodTrackingDetails.sort((a, b) => b.date!.compareTo(a.date!));

      // Restore the focused day instead of jumping to today
      _focusedDay = preservedFocusedDay;
      await selectDay(_focusedDay, false);

      // Update the periodTrackingList and the stream controller
      periodTrackingList = periodTrackingDetails;
      _periodTrackingListController.add(periodTrackingList);
      _selectedDaysController.add(_selectedDays);

      return Right(unit);
    } catch (e) {
      print("Error in getPeriodTrackingDetails: $e");
      return Left(PeriodTrackingFailure.unexpected());
    }
  }

  List<PeriodTrackingModel> _extractPeriodTrackingData(
      Map<String, dynamic>? monthData,
      DateTime futureDate,
      DateTime earliestPeriodDate) {
    if (monthData == null) return [];

    List<PeriodTrackingModel> periodTrackingDetails = [];

    // Handle the new flattened structure (day_1, day_2, etc.)
    final Map<String, dynamic> daysMap = {};

    // Extract day_X fields from the document
    for (final key in monthData.keys) {
      if (key.startsWith('day_')) {
        daysMap[key] = monthData[key];
      }
    }

    daysMap.forEach((dayKey, dayData) {
      try {
        // Extract day number from day_X format
        final int day = int.parse(dayKey.substring(4)); // Remove 'day_' prefix

        // Extract year and month from the month document
        final String yearMonth = monthData['year_month'] ?? '';
        final parts = yearMonth.split('_');
        if (parts.length != 2) return;

        final int year = int.parse(parts[0]);
        final int month = int.parse(parts[1]);
        final DateTime entryDate = DateTime(year, month, day);

        // Include entries within the date range (from earliest period date to future date)
        if (entryDate.isAfter(earliestPeriodDate.subtract(Duration(days: 1))) &&
            entryDate.isBefore(futureDate.add(Duration(days: 1)))) {
          // Handle Timestamp data for PeriodTrackingModel.fromJson compatibility
          final processedDayData =
              Map<String, dynamic>.from(dayData as Map<String, dynamic>);

          // Ensure the date is set to the correct date from the document structure as Timestamp
          processedDayData['date'] = Timestamp.fromDate(entryDate);

          final PeriodTrackingModel periodTrackingModel =
              PeriodTrackingModel.fromJson(processedDayData);
          periodTrackingDetails.add(periodTrackingModel);

          // Also add to _selectedDays if it's marked as selected
          if (dayData['isSelected'] == true) {
            _selectedDays.add(entryDate);
          }
        }
      } catch (e) {
        print('Error parsing day data for $dayKey: $e');
      }
    });

    return periodTrackingDetails;
  }

  Future<Either<PeriodTrackingFailure, Unit>> createPeriodTrackingDetails(
      PeriodTrackingModel periodTrackingDetails) async {
    try {
      final String userId = _firebaseAuth.currentUser!.uid;
      final date = periodTrackingDetails.date;

      if (date == null) {
        print("date is null");
        return Left(PeriodTrackingFailure.invalidData());
      }

      final documentExists = await _checkDocumentExists(userId, date);
      if (documentExists) {
        print("updatePeriodTrackingDetails");
        return updatePeriodTrackingDetails(periodTrackingDetails);
      }
      final docRef = FirebaseFirestore.instance
          .collection('period_tracking')
          .doc(userId)
          .collection('monthly_data')
          .doc('${date.year}_${date.month}');

      // Use consistent structure with day_X format
      final dayData = periodTrackingDetails.toJson();

      docRef.set({
        'year_month': '${date.year}_${date.month}',
        'day_${date.day}': dayData,
      }, SetOptions(merge: true));
      return Right(unit);
    } catch (e) {
      print(e.toString());
      return Left(PeriodTrackingFailure.unexpected());
    }
  }

  //set the last period date
  Future<Either<PeriodTrackingFailure, Unit>> setLastPeriodDate(
      DateTime lastPeriodDate) async {
    try {
      final String userId = _firebaseAuth.currentUser!.uid;
      final userDoc = await _firestore.collection('users').doc(userId).get();
      if (!userDoc.exists) {
        return Left(PeriodTrackingFailure.notFound());
      }

      await _firestore.collection('users').doc(userId).update({
        'healthData.lastPeriodDate': Timestamp.fromDate(lastPeriodDate),
      });
      _lastPeriodDate = lastPeriodDate;
      _lastPeriodDateController.add(_lastPeriodDate);
      return Right(unit);
    } catch (e) {
      return Left(PeriodTrackingFailure.unexpected());
    }
  }

  // Deletes a specific period tracking from the user's document.
  Future<Either<PeriodTrackingFailure, Unit>> deletePeriodTrackingDetails(
      PeriodTrackingModel periodTrackingDetails) async {
    try {
      final String userId = _firebaseAuth.currentUser!.uid;
      final date = periodTrackingDetails.date;

      if (date == null) {
        return Left(PeriodTrackingFailure.invalidData());
      }

      final documentExists = await _checkDocumentExists(userId, date);
      if (!documentExists) {
        return Left(PeriodTrackingFailure.notFound());
      }

      final docRef = FirebaseFirestore.instance
          .collection('period_tracking')
          .doc(userId)
          .collection('monthly_data')
          .doc('${date.year}_${date.month}');

      await docRef.update({
        'day_${date.day}': FieldValue.delete(),
      });

      return Right(unit);
    } catch (e) {
      return Left(PeriodTrackingFailure.unexpected());
    }
  }

  Future<Either<PeriodTrackingFailure, Unit>> updatePeriodTrackingDetails(
      PeriodTrackingModel periodTrackingDetails) async {
    try {
      final String userId = _firebaseAuth.currentUser!.uid;
      final date = periodTrackingDetails.date;

      if (date == null) {
        return Left(PeriodTrackingFailure.invalidData());
      }

      final documentExists = await _checkDocumentExists(userId, date);
      if (!documentExists) {
        return Left(PeriodTrackingFailure.notFound());
      }

      final docRef = FirebaseFirestore.instance
          .collection('period_tracking')
          .doc(userId)
          .collection('monthly_data')
          .doc('${date.year}_${date.month}');

      docRef.update({
        'day_${date.day}': periodTrackingDetails.toJson(),
      });

      return Right(unit);
    } catch (e) {
      return Left(PeriodTrackingFailure.unexpected());
    }
  }

  Future<bool> _checkDocumentExists(String userId, DateTime date) async {
    final docRef = FirebaseFirestore.instance
        .collection('period_tracking')
        .doc(userId)
        .collection('monthly_data')
        .doc('${date.year}_${date.month}');

    final docSnapshot = await docRef.get();
    return docSnapshot.exists;
  }

  @override
  Future<Either<PeriodTrackingFailure, Unit>> selectDay(
      DateTime selectedDay, bool format) async {
    try {
      if (format) {
        print('format - editing calendar');

        // Normalize dates to remove time component for comparison
        final selectedDateOnly =
            DateTime(selectedDay.year, selectedDay.month, selectedDay.day);
        final today = DateTime.now();
        final todayOnly = DateTime(today.year, today.month, today.day);

        // Don't allow editing future dates - they are calculated
        if (selectedDateOnly.isAfter(todayOnly)) {
          print(
              'Cannot edit future dates: $selectedDateOnly is after $todayOnly');
          return Left(PeriodTrackingFailure.invalidData());
        }

        // Check if this date is already selected (using normalized comparison)
        final isCurrentlySelected = _selectedDays.any((date) =>
            date.year == selectedDateOnly.year &&
            date.month == selectedDateOnly.month &&
            date.day == selectedDateOnly.day);

        if (isCurrentlySelected) {
          // Remove the date
          _selectedDays.removeWhere((date) =>
              date.year == selectedDateOnly.year &&
              date.month == selectedDateOnly.month &&
              date.day == selectedDateOnly.day);
          _pendingSelectedDays.removeWhere((date) =>
              date.year == selectedDateOnly.year &&
              date.month == selectedDateOnly.month &&
              date.day == selectedDateOnly.day);
          _pendingRemovedDays.add(selectedDateOnly);
          print('Removed date: $selectedDateOnly');
        } else {
          // Add the date
          _selectedDays.add(selectedDateOnly);
          _pendingSelectedDays.add(selectedDateOnly);
          _pendingRemovedDays.removeWhere((date) =>
              date.year == selectedDateOnly.year &&
              date.month == selectedDateOnly.month &&
              date.day == selectedDateOnly.day);
          print('Added date: $selectedDateOnly');
        }

        print('Selected days after toggle: $_selectedDays');
        print('Pending selected: $_pendingSelectedDays');
        print('Pending removed: $_pendingRemovedDays');

        // Update UI immediately without waiting for Firestore
        _selectedDaysController.add(_selectedDays);
      } else {
        // Just focus on the day for viewing details - normalize to midnight
        _focusedDay =
            DateTime(selectedDay.year, selectedDay.month, selectedDay.day);

        _selectedPeriodTrackingDay = periodTrackingList.firstWhere(
          (element) =>
              element.date?.day == _focusedDay.day &&
              element.date?.month == _focusedDay.month &&
              element.date?.year == _focusedDay.year,
          orElse: () {
            print('❌ No matching model found, creating empty one');
            // Normalize the date to midnight for consistency
            final normalizedDate =
                DateTime(_focusedDay.year, _focusedDay.month, _focusedDay.day);
            PeriodTrackingModel newPeriodTrackingModel =
                PeriodTrackingModel.empty().copyWith(date: normalizedDate);
            periodTrackingList.add(newPeriodTrackingModel);
            return newPeriodTrackingModel;
          },
        );

        print(
            '✅ Found model with ${_selectedPeriodTrackingDay.symptoms?.length ?? 0} symptoms');
        print(
            'selectedPeriodTrackingDay: ${_selectedPeriodTrackingDay.toJson()}');
      }

      _focusedDayController.add(_focusedDay);
      _selectedPeriodTrackingDayController.add(_selectedPeriodTrackingDay);
      return const Right(unit);
    } catch (e) {
      // Handle any exceptions
      print(e.toString());
      return Left(PeriodTrackingFailure.unexpected());
    }
  }

  @override
  Stream<Either<PeriodTrackingFailure, PeriodTrackingData>>
      watchAllPeriodTrackingData() {
    try {
      print("watchAllPeriodTrackingData");
      return Rx.combineLatest5(
        _focusedDayController.stream,
        _selectedDaysController.stream,
        _selectedPeriodTrackingDayController.stream,
        _periodTrackingListController.stream,
        _ovulationDatesController.stream,
        (DateTime focusedDay,
            Set<DateTime> selectedDays,
            PeriodTrackingModel selectedPeriodTrackingDay,
            List<PeriodTrackingModel> periodTrackingList,
            Set<DateTime> ovulationDates) {
          return Right(PeriodTrackingData(
            focusedDay: focusedDay,
            selectedDays: selectedDays,
            selectedPeriodTrackingDay: selectedPeriodTrackingDay,
            periodTrackingDetails: periodTrackingList,
            ovulationDays: ovulationDates,
          ));
        },
      );
    } catch (e) {
      return Stream.value(Left(PeriodTrackingFailure.unexpected()));
    }
  }

  Future<void> dispose() async {
    _clearCache();
    _selectedDaysController.close();
    _focusedDayController.close();
    _periodTrackingListController.close();
    _selectedPeriodTrackingDayController.close();
    _ovulationDatesController.close();
  }

  @override
  Future<Either<PeriodTrackingFailure, Unit>> toggleSymptom(
      SymptomModel symptom) async {
    try {
      print("toggleSymptom");
      // Create a modifiable copy of the symptoms list
      final updatedSymptoms =
          List<SymptomModel>.from(_selectedPeriodTrackingDay.symptoms!);

      // Check if a symptom with the same name exists
      final symptomExists = updatedSymptoms.any((s) => s.name == symptom.name);

      if (symptomExists) {
        updatedSymptoms.removeWhere((s) => s.name == symptom.name);
      } else {
        updatedSymptoms.add(symptom);
      }

      // Update the state with the modified list
      _selectedPeriodTrackingDay =
          _selectedPeriodTrackingDay.copyWith(symptoms: updatedSymptoms);
      _updatePeriodTrackingList(_selectedPeriodTrackingDay);
      _selectedPeriodTrackingDayController.add(_selectedPeriodTrackingDay);

      // Cache the change locally - will be saved when user clicks "Done"
      _cacheSymptomChange(_selectedPeriodTrackingDay);

      return const Right(unit);
    } catch (e) {
      // Handle any exceptions
      print(e.toString());
      return Left(PeriodTrackingFailure.unexpected());
    }
  }

  @override
  Future<Either<PeriodTrackingFailure, Unit>> changeFlowLevel(
      int flowLevel) async {
    try {
      print("changeFlowLevel");
      // Update the state with the new flow level
      _selectedPeriodTrackingDay =
          _selectedPeriodTrackingDay.copyWith(flowLevel: flowLevel);
      _updatePeriodTrackingList(_selectedPeriodTrackingDay);
      _selectedPeriodTrackingDayController.add(_selectedPeriodTrackingDay);

      // Cache the change locally - will be saved when user clicks "Done"
      _cacheSymptomChange(_selectedPeriodTrackingDay);

      return const Right(unit);
    } catch (e) {
      // Handle any exceptions
      print(e.toString());
      return Left(PeriodTrackingFailure.unexpected());
    }
  }

  @override
  Future<Either<PeriodTrackingFailure, Unit>> changePainLevel(
      int painLevel) async {
    try {
      print("changePainLevel");
      // Update the state with the new pain level
      _selectedPeriodTrackingDay =
          _selectedPeriodTrackingDay.copyWith(painLevel: painLevel);
      _updatePeriodTrackingList(_selectedPeriodTrackingDay);
      _selectedPeriodTrackingDayController.add(_selectedPeriodTrackingDay);

      // Cache the change locally - will be saved when user clicks "Done"
      _cacheSymptomChange(_selectedPeriodTrackingDay);

      return const Right(unit);
    } catch (e) {
      // Handle any exceptions
      print(e.toString());
      return Left(PeriodTrackingFailure.unexpected());
    }
  }

  // Cache symptom/pain/flow changes locally
  void _cacheSymptomChange(PeriodTrackingModel model) {
    if (model.date != null) {
      // Normalize the date to midnight for consistency
      final normalizedDate =
          DateTime(model.date!.year, model.date!.month, model.date!.day);
      final normalizedModel = model.copyWith(date: normalizedDate);

      final monthKey = '${normalizedDate.year}_${normalizedDate.month}';
      final dayKey = 'day_${normalizedDate.day}';

      if (!_cachedMonthlyData.containsKey(monthKey)) {
        _cachedMonthlyData[monthKey] = <String, dynamic>{
          'year_month': monthKey,
        };
      }

      // Get the JSON representation - model.toJson() now handles Timestamp conversion correctly
      final modelJson = normalizedModel.toJson();

      // Add isSelected field to maintain consistency with period date structure
      modelJson['isSelected'] = true;

      // Store directly under the flattened key structure
      _cachedMonthlyData[monthKey]![dayKey] = modelJson;

      print('💾 Cached symptom change for $dayKey: ${modelJson['symptoms']}');
    }
  }

  void _updatePeriodTrackingList(PeriodTrackingModel updatedModel) {
    final index = periodTrackingList
        .indexWhere((model) => model.date == updatedModel.date);
    if (index != -1) {
      periodTrackingList[index] = updatedModel;
      _periodTrackingListController.add(periodTrackingList);
    }
  }

  @override
  Future<Either<PeriodTrackingFailure, Unit>> calculateOvulationDays() async {
    try {
      _ovulationDates.clear(); // Clear previous ovulation dates

      // Separate past and future dates
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);

      List<DateTime> sortedPeriodDates = _selectedDays.toList()..sort();
      if (sortedPeriodDates.isEmpty) {
        _ovulationDatesController.add(_ovulationDates);
        return const Right(unit);
      }

      // Separate past and future period dates
      List<DateTime> pastPeriodDates = sortedPeriodDates.where((date) {
        final dateOnly = DateTime(date.year, date.month, date.day);
        return dateOnly.isBefore(today) || dateOnly.isAtSameMomentAs(today);
      }).toList();

      List<DateTime> futurePeriodDates = sortedPeriodDates.where((date) {
        final dateOnly = DateTime(date.year, date.month, date.day);
        return dateOnly.isAfter(today);
      }).toList();

      // Group consecutive days into period cycles (past dates only for accurate calculation)
      List<List<DateTime>> periodCycles = [];
      if (pastPeriodDates.isNotEmpty) {
        List<DateTime> currentCycle = [pastPeriodDates.first];

        for (int i = 1; i < pastPeriodDates.length; i++) {
          DateTime currentDate = pastPeriodDates[i];
          DateTime previousDate = pastPeriodDates[i - 1];

          // If dates are consecutive (within 2 days), consider them the same cycle
          if (currentDate.difference(previousDate).inDays <= 2) {
            currentCycle.add(currentDate);
          } else {
            // Start a new cycle
            if (currentCycle.length >= 1) {
              periodCycles.add(currentCycle);
            }
            currentCycle = [currentDate];
          }
        }

        // Add the last cycle
        if (currentCycle.length >= 1) {
          periodCycles.add(currentCycle);
        }
      }

      // Validate and merge cycles to prevent multiple ovulations per month
      periodCycles = _validateAndMergeCycles(periodCycles);

      // Calculate ovulation for each completed cycle (past cycles only)
      for (int i = 0; i < periodCycles.length; i++) {
        // Find actual period start (first day with flow level 3+ in the cycle)
        DateTime periodStart = _findActualPeriodStart(periodCycles[i]);

        // Calculate cycle length from the difference between actual period starts
        int actualCycleLength = _cycleLength;
        if (i < periodCycles.length - 1) {
          DateTime nextPeriodStart =
              _findActualPeriodStart(periodCycles[i + 1]);
          actualCycleLength = nextPeriodStart.difference(periodStart).inDays;
          // Ensure reasonable cycle length bounds (21-45 days)
          actualCycleLength = actualCycleLength.clamp(21, 45);
        }

        // Ovulation typically occurs 14 days before the next period
        DateTime ovulationDate =
            periodStart.add(Duration(days: actualCycleLength - 14));

        // Only add past ovulation dates (not future predictions here)
        if (ovulationDate.isBefore(today) ||
            ovulationDate.isAtSameMomentAs(today)) {
          // Add 5-day fertility window (2 before peak, peak day, 2 after peak)
          for (int j = -2; j <= 2; j++) {
            DateTime ovulationWindowDay = ovulationDate.add(Duration(days: j));
            _ovulationDates.add(DateTime(
              ovulationWindowDay.year,
              ovulationWindowDay.month,
              ovulationWindowDay.day,
            ));
          }
        }
      }

      // Calculate future ovulation dates based on predicted cycles
      if (periodCycles.isNotEmpty) {
        DateTime lastPeriodStart = periodCycles.last.first;

        // Calculate multiple future cycles (next 6 months)
        for (int cycleIndex = 1; cycleIndex <= 6; cycleIndex++) {
          DateTime futurePeriodStart =
              lastPeriodStart.add(Duration(days: _cycleLength * cycleIndex));
          DateTime futureOvulationDate =
              futurePeriodStart.subtract(const Duration(days: 14));

          // Only add if the ovulation date is in the future
          if (futureOvulationDate.isAfter(today)) {
            // Add 5-day fertility window for future cycles
            for (int j = -2; j <= 2; j++) {
              DateTime ovulationWindowDay =
                  futureOvulationDate.add(Duration(days: j));
              _ovulationDates.add(DateTime(
                ovulationWindowDay.year,
                ovulationWindowDay.month,
                ovulationWindowDay.day,
              ));
            }
            print(
                'Added future ovulation window for cycle $cycleIndex: ${futureOvulationDate.toString().substring(0, 10)}');
          }
        }
      }

      // If no historical data, calculate based on current cycle settings
      if (periodCycles.isEmpty && _lastPeriodDate != null) {
        DateTime baseOvulationDate =
            _lastPeriodDate.add(Duration(days: _cycleLength - 14));

        // Calculate for next few cycles
        for (int cycleIndex = 0; cycleIndex < 6; cycleIndex++) {
          DateTime ovulationDate =
              baseOvulationDate.add(Duration(days: _cycleLength * cycleIndex));

          if (ovulationDate.isAfter(today)) {
            // Add 5-day fertility window
            for (int j = -2; j <= 2; j++) {
              DateTime ovulationWindowDay =
                  ovulationDate.add(Duration(days: j));
              _ovulationDates.add(DateTime(
                ovulationWindowDay.year,
                ovulationWindowDay.month,
                ovulationWindowDay.day,
              ));
            }
          }
        }
      }

      print("selectedDates: $_selectedDays");
      print("ovulationDates: $_ovulationDates");
      _ovulationDatesController.add(_ovulationDates);

      return const Right(unit);
    } catch (e) {
      // Handle any exceptions
      print(e.toString());
      return Left(PeriodTrackingFailure.unexpected());
    }
  }

  @override
  Stream<Either<PeriodTrackingFailure, MenstrualCycleData>>
      getMenstrualCycle() async* {
    try {
      // Fetch initial data
      final initialDataResult = await getInitialMenstrualCycleData();
      yield initialDataResult;

      // Listen to updates from the lastPeriodDateStream
      await for (final lastPeriodDate in lastPeriodDateStream) {
        const int ovulationDayStart = 14;
        const int ovulationDaysLength = 7;

        final DateTime now = DateTime.now();
        final int currentCycleDay =
            now.difference(lastPeriodDate).inDays % _cycleLength + 1;

        final MenstrualCycleData menstrualCycleData = MenstrualCycleData(
          currentCycleDay: currentCycleDay,
          periodDays: _periodLength,
          cycleLength: _cycleLength,
          ovulationDayStart: ovulationDayStart,
          ovulationDaysLength: ovulationDaysLength,
        );

        yield Right(menstrualCycleData);
      }
    } catch (e) {
      yield Left(PeriodTrackingFailure.unexpected());
    }
  }

  @override
  Future<Either<PeriodTrackingFailure, MenstrualCycleData>>
      getInitialMenstrualCycleData() async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) return Left(PeriodTrackingFailure.notFound());

      final userDoc = await _firestore.collection('users').doc(user.uid).get();
      if (!userDoc.exists) return Left(PeriodTrackingFailure.notFound());

      HealthDataModel healthData =
          HealthDataModel.fromJson(userDoc.data()!['healthData']);

      // Calculate the period start date for the current month
      DateTime currentMonthStart =
          DateTime(DateTime.now().year, DateTime.now().month, 1);
      DateTime lastPeriodDate = healthData.lastPeriodDate ?? DateTime.now();
      _cycleLength = healthData.cycleLength ?? 28;

      _periodLength = healthData.periodLength ?? 6;

      // Calculate the number of cycles since the last period date
      int cyclesSinceLastPeriod =
          (DateTime.now().difference(lastPeriodDate).inDays / _cycleLength)
              .floor();

      // Determine the start date of the current cycle
      DateTime currentCycleStartDate = lastPeriodDate
          .add(Duration(days: cyclesSinceLastPeriod * _cycleLength));

      // If the current cycle start date is before the current month, move to the next cycle
      if (currentCycleStartDate.isBefore(currentMonthStart)) {
        currentCycleStartDate =
            currentCycleStartDate.add(Duration(days: _cycleLength));
      }

      // Update _lastPeriodDate if necessary
      if (currentCycleStartDate.isAfter(currentMonthStart)) {
        await setLastPeriodDate(currentCycleStartDate);
      }

      // Update the _lastPeriodDate stream
      _lastPeriodDate = currentCycleStartDate;
      _lastPeriodDateController.add(currentCycleStartDate);

      return Right(MenstrualCycleData(
        currentCycleDay:
            DateTime.now().difference(_lastPeriodDate).inDays % _cycleLength +
                1,
        periodDays: _periodLength,
        cycleLength: _cycleLength,
        ovulationDayStart: 14,
        ovulationDaysLength: 7,
      ));
    } catch (e) {
      return Left(PeriodTrackingFailure.unexpected());
    }
  }

  @override
  Future<Either<PeriodTrackingFailure, Unit>>
      savePeriodDatesAndRecalculate() async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) return Left(PeriodTrackingFailure.unauthenticated());

      print('savePeriodDatesAndRecalculate called - starting batch write');
      print('User ID: ${user.uid}');
      print('User email: ${user.email}');
      print('Current selected days: $_selectedDays');
      print('Pending selected days: $_pendingSelectedDays');
      print('Pending removed days: $_pendingRemovedDays');

      // Build the final set of selected days including pending changes
      Set<DateTime> finalSelectedDays = Set<DateTime>.from(_selectedDays);

      // Add pending selected days (new additions)
      finalSelectedDays.addAll(_pendingSelectedDays);
      print('Added ${_pendingSelectedDays.length} pending selected days');

      // Remove pending removed days (user unselected these)
      for (final removedDate in _pendingRemovedDays) {
        finalSelectedDays.removeWhere((date) =>
            date.year == removedDate.year &&
            date.month == removedDate.month &&
            date.day == removedDate.day);
      }
      print('Removed ${_pendingRemovedDays.length} pending removed days');

      // Convert to sorted list to identify period cycles
      List<DateTime> sortedSelectedDays = finalSelectedDays.toList()..sort();

      if (sortedSelectedDays.isEmpty) {
        print('No data to save');
        return Right(unit);
      }

      // Separate past/present dates from future dates
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);

      List<DateTime> pastPeriodDates = sortedSelectedDays.where((date) {
        final dateOnly = DateTime(date.year, date.month, date.day);
        return dateOnly.isBefore(today) || dateOnly.isAtSameMomentAs(today);
      }).toList();

      // Group consecutive period dates into cycles (only for past dates)
      List<List<DateTime>> periodCycles = [];
      if (pastPeriodDates.isNotEmpty) {
        List<DateTime> currentCycle = [pastPeriodDates.first];

        for (int i = 1; i < pastPeriodDates.length; i++) {
          DateTime currentDate = pastPeriodDates[i];
          DateTime previousDate = pastPeriodDates[i - 1];

          // If dates are consecutive (within 2 days), add to current cycle
          if (currentDate.difference(previousDate).inDays <= 2) {
            currentCycle.add(currentDate);
          } else {
            // Start a new cycle if gap is more than 2 days
            periodCycles.add(currentCycle);
            currentCycle = [currentDate];
          }
        }
        periodCycles.add(currentCycle);
      }

      // Validate and merge cycles to prevent multiple ovulations per month
      periodCycles = _validateAndMergeCycles(periodCycles);

      // Calculate cycle length and period length from historical data
      int calculatedCycleLength = _cycleLength; // Default
      int calculatedPeriodLength = _periodLength; // Default

      if (periodCycles.length >= 2) {
        // Calculate average cycle length from multiple cycles using actual period starts
        List<int> cycleLengths = [];
        for (int i = 1; i < periodCycles.length; i++) {
          DateTime currentPeriodStart = _findActualPeriodStart(periodCycles[i]);
          DateTime previousPeriodStart =
              _findActualPeriodStart(periodCycles[i - 1]);
          int cycleLength =
              currentPeriodStart.difference(previousPeriodStart).inDays;
          if (cycleLength > 15 && cycleLength < 45) {
            // Reasonable cycle length
            cycleLengths.add(cycleLength);
          }
        }

        if (cycleLengths.isNotEmpty) {
          calculatedCycleLength =
              (cycleLengths.reduce((a, b) => a + b) / cycleLengths.length)
                  .round();
          print(
              'Calculated cycle length from data: $calculatedCycleLength days');
        }

        // Calculate average period length
        List<int> periodLengths =
            periodCycles.map((cycle) => cycle.length).toList();
        if (periodLengths.isNotEmpty) {
          calculatedPeriodLength =
              (periodLengths.reduce((a, b) => a + b) / periodLengths.length)
                  .round();
          print(
              'Calculated period length from data: $calculatedPeriodLength days');
        }
      }

      // Update cycle and period lengths
      _cycleLength = calculatedCycleLength;
      _periodLength = calculatedPeriodLength;

      // Find the most recent period cycle to determine the last period start date
      DateTime? lastPeriodDate;
      if (periodCycles.isNotEmpty) {
        lastPeriodDate = periodCycles.last.first;
        print('Last period date determined: $lastPeriodDate');
      }

      // Use optimized batch write to save all changes at once
      await _performBatchPeriodDataWrite(user.uid);

      // Clear existing selected days and recalculate
      _selectedDays.clear();
      _futurePredictionsAdded = false; // Reset flag when recalculating
      print('🧹 Cleared _selectedDays, now has: ${_selectedDays.length} dates');

      // Add the final selected days (which already include pending changes)
      _selectedDays.addAll(finalSelectedDays);
      print(
          '📝 After processing all changes, _selectedDays has: ${_selectedDays.length} dates');

      // Calculate future period dates using consolidated method for consistency
      // print(
      //     '🔮 Before _calculateAndAddFuturePeriods: ${_selectedDays.length} dates');
      await _calculateAndAddFuturePeriods();
      // print(
      //     '🔮 After _calculateAndAddFuturePeriods: ${_selectedDays.length} dates');

      // Update last period date and save to user profile
      if (lastPeriodDate != null) {
        await setLastPeriodDate(lastPeriodDate);
        _lastPeriodDate = lastPeriodDate;
        _lastPeriodDateController.add(_lastPeriodDate);

        // Update user's health data with calculated cycle and period lengths
        await _updateUserHealthData(
            lastPeriodDate, _cycleLength, _periodLength);

        print(
            'Updated menstrual cycle data: cycle=$_cycleLength, period=$_periodLength, lastPeriod=$lastPeriodDate');
      }

      // Print final calculated dates before sending to UI
      print(
          '🎯 FINAL CALCULATED DATES - Total selected days: ${_selectedDays.length}');
      List<DateTime> sortedFinalDays = _selectedDays.toList()..sort();
      for (int i = 0; i < sortedFinalDays.length; i++) {
        print('🎯   ${i + 1}. ${sortedFinalDays[i]}');
      }

      // Update streams
      print('📡 Sending ${_selectedDays.length} dates to stream');
      _selectedDaysController.add(_selectedDays);

      // Recalculate ovulation dates
      await calculateOvulationDays();

      // Save to local storage
      await _saveToLocalStorage();

      // Reschedule notifications based on updated period data
      await _rescheduleNotificationsAfterPeriodUpdate();

      print('Period dates saved successfully to Firestore and local storage');
      return Right(unit);
    } catch (e) {
      print('Error in savePeriodDatesAndRecalculate: $e');
      return Left(PeriodTrackingFailure.unexpected());
    }
  }

  @override
  bool isDateInFuture(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final compareDate = DateTime(date.year, date.month, date.day);
    return compareDate.isAfter(today);
  }

  // Update user's health data with calculated cycle and period information
  Future<void> _updateUserHealthData(
      DateTime lastPeriodDate, int cycleLength, int periodLength) async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) return;

      // Calculate next period start date and next ovulation date
      DateTime nextPeriodStartDate =
          lastPeriodDate.add(Duration(days: cycleLength));
      DateTime nextOvulationDate =
          lastPeriodDate.add(Duration(days: cycleLength - 14));

      await _firestore.collection('users').doc(user.uid).update({
        'healthData.lastPeriodDate': Timestamp.fromDate(lastPeriodDate),
        'healthData.cycleLength': cycleLength,
        'healthData.periodLength': periodLength,
        'healthData.nextPeriodStartDate':
            Timestamp.fromDate(nextPeriodStartDate),
        'healthData.nextOvulationDate': Timestamp.fromDate(nextOvulationDate),
        'healthData.lastUpdated': Timestamp.now(),
      });

      // Also save to local storage
      await _saveToLocalStorage();

      print(
          'Updated user health data: lastPeriod=$lastPeriodDate, cycle=$cycleLength, period=$periodLength');
      print(
          'Next period: $nextPeriodStartDate, Next ovulation: $nextOvulationDate');
    } catch (e) {
      print('Error updating user health data: $e');
    }
  }

  // Local storage methods
  Future<void> _saveToLocalStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Save selected days as ISO strings
      final selectedDaysStrings =
          _selectedDays.map((date) => date.toIso8601String()).toList();
      await prefs.setStringList(_selectedDaysKey, selectedDaysStrings);

      // Save ovulation dates as ISO strings
      final ovulationDatesStrings =
          _ovulationDates.map((date) => date.toIso8601String()).toList();
      await prefs.setStringList(_ovulationDatesKey, ovulationDatesStrings);

      // Save other data
      await prefs.setString(
          _lastPeriodDateKey, _lastPeriodDate.toIso8601String());
      await prefs.setInt(_cycleLengthKey, _cycleLength);
      await prefs.setInt(_periodLengthKey, _periodLength);
      await prefs.setString(_lastSyncKey, DateTime.now().toIso8601String());

      print('Period tracking data saved to local storage');
    } catch (e) {
      print('Error saving to local storage: $e');
    }
  }

  // Local storage loading removed - Firestore is now the single source of truth

  Future<void> _clearLocalStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_selectedDaysKey);
      await prefs.remove(_ovulationDatesKey);
      await prefs.remove(_lastPeriodDateKey);
      await prefs.remove(_cycleLengthKey);
      await prefs.remove(_periodLengthKey);
      await prefs.remove(_lastSyncKey);
      print('Local storage cleared');
    } catch (e) {
      print('Error clearing local storage: $e');
    }
  }

  // Load period data from Firestore (fallback when local storage is empty)
  Future<void> _loadPeriodDataFromFirestore() async {
    try {
      print(
          '🔥 _loadPeriodDataFromFirestore called! Current _selectedDays: ${_selectedDays.length}');
      final user = _firebaseAuth.currentUser;
      if (user == null) return;

      print('Loading period data from Firestore for user: ${user.uid}');
      print('User email: ${user.email}');

      // Check if data exists in alternative locations
      await _checkAlternativeDataLocations(user.uid);

      // Load data from the last 12 months + current month to get comprehensive period history
      // This ensures better cross-device sync and includes current month
      final now = DateTime.now();
      final twelveMonthsAgo = DateTime(now.year, now.month - 12, now.day);

      // Include current month by adding 1 to the loop count
      for (int i = 0; i <= 12; i++) {
        final targetDate =
            DateTime(twelveMonthsAgo.year, twelveMonthsAgo.month + i, 1);
        final monthKey = '${targetDate.year}_${targetDate.month}';

        try {
          final docRef = _firestore
              .collection('period_tracking')
              .doc(user.uid)
              .collection('monthly_data')
              .doc(monthKey);

          print(
              'Checking Firestore document: period_tracking/${user.uid}/monthly_data/$monthKey');

          final docSnapshot = await docRef.get();

          if (docSnapshot.exists) {
            print('✅ Document exists: $monthKey');
            final data = docSnapshot.data() as Map<String, dynamic>? ?? {};
            print('Document data keys: ${data.keys.toList()}');

            // Handle only the new flattened structure (day_1, day_2, etc.)
            Map<String, dynamic> days = {};

            // Extract day_X fields from the document
            for (final key in data.keys) {
              if (key.startsWith('day_')) {
                days[key] = data[key];
              }
            }
            print('Found ${days.length} day entries in document');

            print('Days data keys: ${days.keys.toList()}');

            // Cache the data with the same structure as Firestore (flattened)
            _cachedMonthlyData[monthKey] = {
              'year_month': monthKey,
              ...days, // Spread the day_X keys directly into the document
              ...data, // Include any other fields from Firestore
            };

            // Extract period dates and create PeriodTrackingModel objects - only handle day_X format
            for (final dayEntry in days.entries) {
              try {
                final dayData = dayEntry.value as Map<String, dynamic>;
                final isSelected = dayData['isSelected'] as bool? ?? false;

                // Extract day number from day_X format
                final dayNumber = int.parse(
                    dayEntry.key.substring(4)); // Remove 'day_' prefix
                final periodDate =
                    DateTime(targetDate.year, targetDate.month, dayNumber);

                // Create PeriodTrackingModel from the day data
                // Handle Timestamp data for PeriodTrackingModel.fromJson compatibility
                final processedDayData = Map<String, dynamic>.from(dayData);

                // Ensure the date is set to the correct date from the document structure as Timestamp
                processedDayData['date'] = Timestamp.fromDate(periodDate);

                final periodTrackingModel =
                    PeriodTrackingModel.fromJson(processedDayData);

                // Add to periodTrackingList if it doesn't already exist
                final existingIndex = periodTrackingList.indexWhere((model) =>
                    model.date?.year == periodDate.year &&
                    model.date?.month == periodDate.month &&
                    model.date?.day == periodDate.day);

                if (existingIndex == -1) {
                  periodTrackingList.add(periodTrackingModel);
                  print(
                      'Added PeriodTrackingModel for $periodDate with ${periodTrackingModel.symptoms?.length ?? 0} symptoms');
                } else {
                  // Update existing model with latest data
                  periodTrackingList[existingIndex] = periodTrackingModel;
                  print(
                      'Updated PeriodTrackingModel for $periodDate with ${periodTrackingModel.symptoms?.length ?? 0} symptoms');
                }

                if (isSelected) {
                  _selectedDays.add(periodDate);
                  print(
                      'Found period date: $periodDate (isSelected: $isSelected)');
                } else {
                  // Log deselected dates for debugging
                  print(
                      'Found deselected date: $periodDate (isSelected: $isSelected)');
                }
              } catch (e) {
                print('Error parsing day data for ${dayEntry.key}: $e');
              }
            }
          } else {
            print('❌ Document does not exist: $monthKey');
          }
        } catch (e) {
          print('Error loading month $monthKey: $e');
        }
      }

      print('Loaded ${_selectedDays.length} period dates from Firestore');
      print(
          'Loaded ${periodTrackingList.length} period tracking models from Firestore');
      print(
          'Firestore period dates: ${_selectedDays.take(10).toList()}'); // Show first 10 for debugging

      // Update the stream controller with the loaded data
      _periodTrackingListController.add(periodTrackingList);

      // Save to local storage for future use
      await _saveToLocalStorage();
    } catch (e) {
      print('Error loading period data from Firestore: $e');
    }
  }

  // Check alternative data locations in Firestore
  Future<void> _checkAlternativeDataLocations(String userId) async {
    try {
      print('🔍 Checking alternative data locations...');

      // Check if data exists in root period_tracking collection
      final rootDoc =
          await _firestore.collection('period_tracking').doc(userId).get();
      if (rootDoc.exists) {
        print('✅ Found data in root period_tracking/$userId');
        print('Root document keys: ${rootDoc.data()?.keys.toList()}');
      } else {
        print('❌ No data in root period_tracking/$userId');
      }

      // Check if data exists in users collection
      final userDoc = await _firestore
          .collection('users')
          .doc(userId)
          .collection('period_tracking')
          .get();
      if (userDoc.docs.isNotEmpty) {
        print(
            '✅ Found ${userDoc.docs.length} documents in users/$userId/period_tracking');
        for (var doc in userDoc.docs.take(3)) {
          print('Document ID: ${doc.id}, keys: ${doc.data().keys.toList()}');
        }
      } else {
        print('❌ No data in users/$userId/period_tracking');
      }
    } catch (e) {
      print('Error checking alternative locations: $e');
    }
  }

  // Sync local changes to cloud with conflict resolution
  Future<bool> _syncToCloud() async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) return false;

      print('Starting cloud sync...');

      // Check if there are pending changes
      if (_pendingSelectedDays.isEmpty && _pendingRemovedDays.isEmpty) {
        print('No pending changes to sync');
        return true;
      }

      // Get the latest cloud data to check for conflicts
      final cloudData = await _getLatestCloudData();

      // Resolve conflicts by merging local and cloud data
      final mergedData = await _resolveDataConflicts(cloudData);

      // Perform batch write with merged data
      await _performBatchPeriodDataWrite(user.uid);

      // Clear pending changes after successful sync
      _pendingSelectedDays.clear();
      _pendingRemovedDays.clear();

      // Update local storage with synced data
      await _saveToLocalStorage();

      print('Cloud sync completed successfully');
      return true;
    } catch (e) {
      print('Error during cloud sync: $e');
      return false;
    }
  }

  // Get latest cloud data for conflict resolution
  Future<Map<String, Set<DateTime>>> _getLatestCloudData() async {
    final user = _firebaseAuth.currentUser;
    if (user == null) return {};

    final cloudSelectedDays = <DateTime>{};
    final now = DateTime.now();

    // Check last 3 months for recent changes
    for (int i = 0; i < 3; i++) {
      final targetDate = DateTime(now.year, now.month - i, 1);
      final monthKey = '${targetDate.year}_${targetDate.month}';

      try {
        final docRef = _firestore
            .collection('period_tracking')
            .doc(user.uid)
            .collection('monthly_data')
            .doc(monthKey);

        final docSnapshot = await docRef.get();

        if (docSnapshot.exists) {
          final data = docSnapshot.data() as Map<String, dynamic>;

          // Handle only the new flattened structure (day_1, day_2, etc.)
          for (final key in data.keys) {
            if (key.startsWith('day_')) {
              final dayData = data[key] as Map<String, dynamic>;
              final isSelected = dayData['isSelected'] as bool? ?? false;

              if (isSelected) {
                final dayNumber =
                    int.parse(key.substring(4)); // Remove 'day_' prefix
                final periodDate =
                    DateTime(targetDate.year, targetDate.month, dayNumber);
                cloudSelectedDays.add(periodDate);
              }
            }
          }
        }
      } catch (e) {
        print('Error loading cloud data for month $monthKey: $e');
      }
    }

    return {'selectedDays': cloudSelectedDays};
  }

  // Resolve conflicts between local and cloud data
  Future<Set<DateTime>> _resolveDataConflicts(
      Map<String, Set<DateTime>> cloudData) async {
    final cloudSelectedDays = cloudData['selectedDays'] ?? <DateTime>{};
    final mergedSelectedDays = <DateTime>{};

    // Start with cloud data as base
    mergedSelectedDays.addAll(cloudSelectedDays);

    // Apply local additions (pending selected days)
    mergedSelectedDays.addAll(_pendingSelectedDays);

    // Apply local removals (pending removed days)
    for (final removedDate in _pendingRemovedDays) {
      mergedSelectedDays.removeWhere((date) =>
          date.year == removedDate.year &&
          date.month == removedDate.month &&
          date.day == removedDate.day);
    }

    // Update local state with merged data
    _selectedDays.clear();
    _selectedDays.addAll(mergedSelectedDays);

    print(
        'Conflict resolution: merged ${mergedSelectedDays.length} period dates');
    return mergedSelectedDays;
  }

  // Optimized batch write method to reduce Firestore operations
  Future<void> _performBatchPeriodDataWrite(String userId) async {
    try {
      print('Starting batch write for user: $userId');
      print('Writing to Firestore collection: period_tracking');
      final batch = _firestore.batch();

      // Only process past dates (not future predictions)
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);

      int batchOperations = 0;

      // Process pending additions
      for (final date in _pendingSelectedDays) {
        final dateOnly = DateTime(date.year, date.month, date.day);
        if (dateOnly.isBefore(today) || dateOnly.isAtSameMomentAs(today)) {
          final docRef = _firestore
              .collection('period_tracking')
              .doc(userId)
              .collection('monthly_data')
              .doc('${date.year}_${date.month}');

          // Update the cache as well
          final monthKey = '${date.year}_${date.month}';
          if (!_cachedMonthlyData.containsKey(monthKey)) {
            _cachedMonthlyData[monthKey] = {
              'year_month': monthKey,
            };
          }

          final cachedData = <String, dynamic>{
            'date': Timestamp.fromDate(date),
            'isSelected': true,
            'symptoms': <dynamic>[],
            'painLevel': 0,
            'flowLevel': 0,
          };

          // Store in cache using the same flattened structure as Firestore
          _cachedMonthlyData[monthKey]!['day_${date.day}'] = cachedData;

          batch.set(
              docRef,
              {
                'year_month': monthKey,
                'day_${date.day}': cachedData,
              },
              SetOptions(merge: true));
          batchOperations++;

          print('Added period date to batch: ${date.toString()}');
        }
      }

      // Process pending removals - set isSelected to false instead of deleting
      for (final date in _pendingRemovedDays) {
        final dateOnly = DateTime(date.year, date.month, date.day);
        if (dateOnly.isBefore(today) || dateOnly.isAtSameMomentAs(today)) {
          final docRef = _firestore
              .collection('period_tracking')
              .doc(userId)
              .collection('monthly_data')
              .doc('${date.year}_${date.month}');

          // Update the cache as well - set isSelected to false
          final monthKey = '${date.year}_${date.month}';
          if (!_cachedMonthlyData.containsKey(monthKey)) {
            _cachedMonthlyData[monthKey] = {
              'year_month': monthKey,
            };
          }

          final cachedData = <String, dynamic>{
            'date': Timestamp.fromDate(date),
            'isSelected': false, // Set to false instead of deleting
            'symptoms': <dynamic>[],
            'painLevel': 0,
            'flowLevel': 0,
          };

          // Store in cache using the same flattened structure as Firestore
          _cachedMonthlyData[monthKey]!['day_${date.day}'] = cachedData;

          batch.set(
              docRef,
              {
                'year_month': monthKey,
                'day_${date.day}': cachedData,
              },
              SetOptions(merge: true));
          batchOperations++;

          print('Set isSelected=false for period date: ${date.toString()}');
        }
      }

      // Process all cached monthly data changes (including symptoms/pain/flow)
      for (final monthKey in _cachedMonthlyData.keys) {
        final monthData = _cachedMonthlyData[monthKey];
        if (monthData == null) continue;

        // Handle flattened structure where keys are like 'day_1', 'day_2', etc.
        for (final key in monthData.keys) {
          if (!key.startsWith('day_')) continue;

          final dayDataRaw = monthData[key];
          if (dayDataRaw == null) continue;

          final dayData = Map<String, dynamic>.from(dayDataRaw as Map);
          final dayKey = key; // This is already 'day_X' format

          // Safely extract the date, handling both Timestamp and DateTime objects
          DateTime date;
          if (dayData['date'] is Timestamp) {
            date = (dayData['date'] as Timestamp).toDate();
          } else if (dayData['date'] is DateTime) {
            date = dayData['date'] as DateTime;
          } else {
            // Skip this entry if date format is unexpected
            continue;
          }

          final dateOnly = DateTime(date.year, date.month, date.day);

          // Only save past/present data
          if (dateOnly.isBefore(today) || dateOnly.isAtSameMomentAs(today)) {
            final docRef = _firestore
                .collection('period_tracking')
                .doc(userId)
                .collection('monthly_data')
                .doc(monthKey);

            // Ensure dayData has the correct format with Timestamp
            final batchData = Map<String, dynamic>.from(dayData);
            if (batchData['date'] is DateTime) {
              batchData['date'] =
                  Timestamp.fromDate(batchData['date'] as DateTime);
            }

            batch.set(
                docRef,
                {
                  'year_month': monthKey,
                  dayKey: batchData, // dayKey is already 'day_X' format
                },
                SetOptions(merge: true));
            batchOperations++;
          }
        }
      }

      // Commit all changes in a single batch
      if (batchOperations > 0) {
        await batch.commit();
        print(
            'Batch write completed successfully with $batchOperations operations');
      } else {
        print('No batch operations to perform');
      }

      // Clear pending changes after successful write
      _pendingSelectedDays.clear();
      _pendingRemovedDays.clear();
    } catch (e) {
      print('Error in batch write: $e');
      rethrow;
    }
  }

  // Clear cache when needed (e.g., when switching users)
  void _clearCache() {
    _cachedMonthlyData.clear();
    _pendingSelectedDays.clear();
    _pendingRemovedDays.clear();
  }

  // Helper method to validate and merge cycles to prevent multiple ovulations per month
  List<List<DateTime>> _validateAndMergeCycles(List<List<DateTime>> cycles) {
    if (cycles.length <= 1) return cycles;

    List<List<DateTime>> validatedCycles = [];

    for (int i = 0; i < cycles.length; i++) {
      DateTime currentCycleStart = _findActualPeriodStart(cycles[i]);

      // Check if this cycle is too close to the previous validated cycle
      bool shouldMerge = false;
      if (validatedCycles.isNotEmpty) {
        DateTime lastValidatedStart =
            _findActualPeriodStart(validatedCycles.last);
        int daysBetween =
            currentCycleStart.difference(lastValidatedStart).inDays;

        // If cycles are less than 15 days apart, merge them (likely spotting)
        if (daysBetween < 15) {
          print(
              '🔄 Merging cycles: ${lastValidatedStart.toString().substring(0, 10)} and ${currentCycleStart.toString().substring(0, 10)} (${daysBetween} days apart)');
          shouldMerge = true;
        }
      }

      if (shouldMerge) {
        // Merge current cycle with the last validated cycle
        validatedCycles.last.addAll(cycles[i]);
        validatedCycles.last.sort(); // Keep dates sorted
        print('🔄 Merged cycle now has ${validatedCycles.last.length} days');
      } else {
        // Add as new cycle if it meets minimum requirements
        if (cycles[i].length >= 2) {
          // At least 2 days for a valid period
          validatedCycles.add(cycles[i]);
          print(
              '✅ Added valid cycle starting: ${currentCycleStart.toString().substring(0, 10)}');
        } else {
          print(
              '❌ Rejected short cycle: ${currentCycleStart.toString().substring(0, 10)} (only ${cycles[i].length} day)');
        }
      }
    }

    // print(
    //     '🎯 Cycle validation complete: ${cycles.length} → ${validatedCycles.length} cycles');
    return validatedCycles;
  }

  // Helper method to find actual period start (excludes spotting days)
  DateTime _findActualPeriodStart(List<DateTime> cycle) {
    // Look for the first day with flow level 3+ (actual period, not spotting)
    for (DateTime date in cycle) {
      final flowLevel = _getFlowLevelForDate(date);
      if (flowLevel >= 3) {
        print('Found actual period start: $date (flow level: $flowLevel)');
        return date;
      }
    }

    // If no heavy flow found, fall back to first date (original behavior)
    print('No heavy flow found in cycle, using first date: ${cycle.first}');
    return cycle.first;
  }

  // Helper method to get flow level for a specific date from cached data
  int _getFlowLevelForDate(DateTime date) {
    final monthKey = '${date.year}_${date.month}';
    final dayKey = '${date.day}';

    if (_cachedMonthlyData.containsKey(monthKey)) {
      final monthData = _cachedMonthlyData[monthKey];
      if (monthData != null && monthData['days'] != null) {
        final daysData = monthData['days'] as Map<String, dynamic>;
        if (daysData.containsKey(dayKey)) {
          final dayData = daysData[dayKey] as Map<String, dynamic>;
          return dayData['flowLevel'] as int? ?? 0;
        }
      }
    }

    return 0; // Default to no flow if data not found
  }

  // Helper method to add future predictions based on saved period data
  Future<void> _addFuturePredictions() async {
    try {
      print(
          '🚨 _addFuturePredictions called! Current _selectedDays: ${_selectedDays.length}');

      // Always recalculate future predictions to ensure they're up to date
      // The _calculateAndAddFuturePeriods method handles removing old predictions
      await _calculateAndAddFuturePeriods();
      _futurePredictionsAdded = true;

      print(
          '🚨 _addFuturePredictions finished! Current _selectedDays: ${_selectedDays.length}');
    } catch (e) {
      print('Error adding future predictions: $e');
    }
  }

  // Consolidated method for calculating future periods - ensures consistency
  Future<void> _calculateAndAddFuturePeriods() async {
    try {
      // First, remove all existing future predictions to avoid duplicates
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);

      // Remove all future dates from _selectedDays
      _selectedDays.removeWhere((date) {
        final dateOnly = DateTime(date.year, date.month, date.day);
        return dateOnly.isAfter(today);
      });

      print(
          '🧹 Removed old future predictions, current _selectedDays: ${_selectedDays.length}');

      // Get saved period dates (past and present only)
      List<DateTime> pastPeriodDates = _selectedDays.where((date) {
        final dateOnly = DateTime(date.year, date.month, date.day);
        return dateOnly.isBefore(today) || dateOnly.isAtSameMomentAs(today);
      }).toList();

      if (pastPeriodDates.isEmpty) return;

      // Group into cycles to find the last period start
      pastPeriodDates.sort();
      List<List<DateTime>> periodCycles = [];
      List<DateTime> currentCycle = [pastPeriodDates.first];

      for (int i = 1; i < pastPeriodDates.length; i++) {
        DateTime currentDate = pastPeriodDates[i];
        DateTime previousDate = pastPeriodDates[i - 1];

        if (currentDate.difference(previousDate).inDays <= 2) {
          currentCycle.add(currentDate);
        } else {
          periodCycles.add(currentCycle);
          currentCycle = [currentDate];
        }
      }
      periodCycles.add(currentCycle);

      // Validate and merge cycles to prevent multiple ovulations per month
      periodCycles = _validateAndMergeCycles(periodCycles);

      // Calculate future predictions using actual period start (not spotting)
      if (periodCycles.isNotEmpty) {
        DateTime lastPeriodStart = _findActualPeriodStart(periodCycles.last);

        // Use the same reference date calculation for consistency
        final referenceDate = DateTime(
            lastPeriodStart.year, lastPeriodStart.month, lastPeriodStart.day);

        DateTime nextPeriodStart =
            lastPeriodStart.add(Duration(days: _cycleLength));

        print(
            '_calculateAndAddFuturePeriods: Last actual period start: $lastPeriodStart');
        print(
            '_calculateAndAddFuturePeriods: Next period start: $nextPeriodStart');

        for (int i = 0; i < 6; i++) {
          DateTime currentPeriodStart =
              nextPeriodStart.add(Duration(days: i * _cycleLength));

          // Only add if the period start is after the last recorded period
          if (currentPeriodStart.isAfter(referenceDate)) {
            // print(
            // '🔮 Adding future period cycle starting: $currentPeriodStart');
            for (int j = 0; j < _periodLength; j++) {
              DateTime periodDay = currentPeriodStart.add(Duration(days: j));
              _selectedDays.add(periodDay);
              // print('🔮   Day ${j + 1}: $periodDay');
            }
            print(
                '_calculateAndAddFuturePeriods: Added future period cycle starting: $currentPeriodStart');
          }
        }
      }
    } catch (e) {
      print('Error adding future predictions: $e');
    }
  }

  // Reminder settings implementation
  @override
  Future<Either<PeriodTrackingFailure, PeriodReminderSettings>>
      getReminderSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = prefs.getString(_reminderSettingsKey);

      if (settingsJson != null) {
        final settingsMap = jsonDecode(settingsJson) as Map<String, dynamic>;
        final settings = PeriodReminderSettings.fromJson(settingsMap);
        return Right(settings);
      } else {
        // Return default settings if none exist
        return Right(PeriodReminderSettings.empty());
      }
    } catch (e) {
      print('Error loading reminder settings: $e');
      return const Left(PeriodTrackingFailure.unexpected());
    }
  }

  @override
  Future<Either<PeriodTrackingFailure, Unit>> saveReminderSettings(
      PeriodReminderSettings settings) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = jsonEncode(settings.toJson());
      await prefs.setString(_reminderSettingsKey, settingsJson);

      // Also save to Firestore for cross-device sync
      final user = _firebaseAuth.currentUser;
      if (user != null) {
        await _firestore
            .collection('users')
            .doc(user.uid)
            .update({'reminderSettings': settings.toJson()});
      }

      // Schedule notifications based on the new settings
      await scheduleNotificationsForSettings(settings);

      return const Right(unit);
    } catch (e) {
      print('Error saving reminder settings: $e');
      return const Left(PeriodTrackingFailure.unexpected());
    }
  }

  @override
  Future<Either<PeriodTrackingFailure, Unit>> scheduleNotificationsForSettings(
      PeriodReminderSettings settings) async {
    try {
      print('Scheduling notifications for settings: ${settings.toJson()}');

      // Cancel existing notifications first
      await cancelAllPeriodNotifications();

      if (!settings.isPeriodReminderEnabled &&
          !settings.isOvulationReminderEnabled) {
        print('No reminders enabled, skipping notification scheduling');
        return const Right(unit);
      }

      // Get future period and ovulation dates
      final futureDates = await _calculateFutureNotificationDates(settings);
      if (futureDates.isEmpty) {
        print('No future dates available for notifications');
        return const Right(unit);
      }

      // Schedule period notifications
      if (settings.isPeriodReminderEnabled && futureDates['periods'] != null) {
        await _schedulePeriodNotifications(
            futureDates['periods']!, settings.periodReminderDaysBefore);
      }

      // Schedule ovulation notifications
      if (settings.isOvulationReminderEnabled &&
          futureDates['ovulations'] != null) {
        await _scheduleOvulationNotifications(
            futureDates['ovulations']!, settings.ovulationReminderDaysBefore);
      }

      print('Successfully scheduled notifications');
      return const Right(unit);
    } catch (e) {
      print('Error scheduling notifications: $e');
      return const Left(PeriodTrackingFailure.unexpected());
    }
  }

  @override
  Future<Either<PeriodTrackingFailure, Unit>>
      cancelAllPeriodNotifications() async {
    try {
      print('Canceling all period notifications');

      // Cancel period notification group
      await _notificationsFacade.disableNotificationGroup('period_reminders');

      // Cancel ovulation notification group
      await _notificationsFacade
          .disableNotificationGroup('ovulation_reminders');

      // Clear the stored notification IDs
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('period_notification_ids');

      print('Canceled all period and ovulation notifications');
      return const Right(unit);
    } catch (e) {
      print('Error canceling notifications: $e');
      return const Left(PeriodTrackingFailure.unexpected());
    }
  }

  // Helper method to calculate future notification dates
  Future<Map<String, DateTime?>> _calculateFutureNotificationDates(
      PeriodReminderSettings settings) async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) {
        print('No user logged in, cannot calculate future dates');
        throw Exception('User not logged in');
      }
      final userDoc = await _firestore.collection('users').doc(user.uid).get();
      final healthDataDoc =
          HealthDataModel.fromJson(userDoc.data()?['healthData']);
      print(
          'Health data loaded: lastPeriodDate=${healthDataDoc.nextPeriodStartDate}, cycleLength=${healthDataDoc.cycleLength}, periodLength=${healthDataDoc.periodLength}');

      if (healthDataDoc.nextPeriodStartDate == null ||
          healthDataDoc.nextOvulationDate == null) {
        throw Exception('Future dates do not exist');
      }

      return {
        'periods': healthDataDoc.nextPeriodStartDate,
        'ovulations': healthDataDoc.nextOvulationDate,
      };
    } catch (e) {
      print('Error calculating future dates: $e');
      return {'periods': null, 'ovulations': null};
    }
  }

  // Helper method to schedule period notifications
  Future<void> _schedulePeriodNotifications(
      DateTime? periodDate, int daysBefore) async {
    try {
      if (periodDate == null) {
        print('No period dates to schedule notifications for');
        return;
      }

      final notificationDate = periodDate.subtract(Duration(days: daysBefore));

      // Only schedule if notification date is in the future
      if (notificationDate.isAfter(DateTime.now())) {
        final notificationId =
            'period_${periodDate.millisecondsSinceEpoch}_$daysBefore';

        // Convert to timezone-aware datetime
        final tzDateTime = tz.TZDateTime.from(
          DateTime(notificationDate.year, notificationDate.month,
              notificationDate.day, 9, 0), // 9 AM
          tz.local,
        );

        // Schedule single notification
        final result = await _notificationsFacade.scheduleSingleNotification(
          body: daysBefore == 1
              ? 'Your period is expected to start tomorrow. Be prepared!'
              : 'Your period is expected to start in $daysBefore days. Be prepared!',
          dateTime: tzDateTime,
          notificationId: notificationId,
          notificationType: 'period_reminder',
          title: 'Period Reminder',
          payload: 'period_reminder:${periodDate.toIso8601String()}',
          isForeground: true,
        );

        result.mapBoth(
          onLeft: (failure) =>
              print('Failed to schedule period notification: $failure'),
          onRight: (_) => print(
              'Successfully scheduled period notification for ${tzDateTime.toIso8601String()}'),
        );
      }
    } catch (e) {
      print('Error scheduling period notifications: $e');
    }
  }

  // Helper method to schedule ovulation notifications
  Future<void> _scheduleOvulationNotifications(
      DateTime? ovulationDate, int daysBefore) async {
    try {
      if (ovulationDate == null) {
        print('No ovulation dates to schedule notifications for');
        return;
      }

      final notificationDate =
          ovulationDate.subtract(Duration(days: daysBefore + 2));

      // Only schedule if notification date is in the future
      if (notificationDate.isAfter(DateTime.now())) {
        final notificationId =
            'ovulation_${ovulationDate.millisecondsSinceEpoch}_$daysBefore';

        // Convert to timezone-aware datetime
        final tzDateTime = tz.TZDateTime.from(
          DateTime(notificationDate.year, notificationDate.month,
              notificationDate.day, 9, 0), // 9 AM
          tz.local,
        );

        // Schedule single notification
        final result = await _notificationsFacade.scheduleSingleNotification(
          body: daysBefore == 1
              ? 'Your ovulation window starts tomorrow. This is your fertile period!'
              : 'Your ovulation window starts in $daysBefore days. This is your fertile period!',
          dateTime: tzDateTime,
          notificationId: notificationId,
          title: 'Ovulation Reminder',
          notificationType: 'ovulation_reminder',
          payload: 'ovulation_reminder:${ovulationDate.toIso8601String()}',
          isForeground: true,
        );

        result.mapBoth(
          onLeft: (failure) =>
              print('Failed to schedule ovulation notification: $failure'),
          onRight: (_) => print(
              'Successfully scheduled ovulation notification for ${tzDateTime.toIso8601String()}'),
        );
      }
    } catch (e) {
      print('Error scheduling ovulation notifications: $e');
    }
  }

  // Helper method to reschedule notifications after period data changes
  Future<void> _rescheduleNotificationsAfterPeriodUpdate() async {
    try {
      print('Rescheduling notifications after period data update');

      // Get current reminder settings
      final settingsResult = await getReminderSettings();

      settingsResult.mapBoth(
        onLeft: (failure) {
          print('Failed to get reminder settings for rescheduling: $failure');
        },
        onRight: (settings) async {
          // Only reschedule if reminders are enabled
          if (settings.isPeriodReminderEnabled ||
              settings.isOvulationReminderEnabled) {
            print(
                'Rescheduling notifications with settings: ${settings.toJson()}');
            await scheduleNotificationsForSettings(settings);
          } else {
            print('No reminders enabled, skipping notification rescheduling');
          }
        },
      );
    } catch (e) {
      print('Error rescheduling notifications: $e');
    }
  }

  @override
  Future<Either<PeriodTrackingFailure, Unit>>
      initializePeriodReminders() async {
    try {
      print('Initializing period reminders system...');

      // Check if we need to sync settings from Firestore first
      await _syncReminderSettingsFromFirestore();

      // Load reminder settings from local storage and Firestore
      final settingsResult = await getReminderSettings();

      final settings = settingsResult.getOrElse((failure) {
        print(
            'Failed to load reminder settings during initialization: $failure');
        return PeriodReminderSettings.empty();
      });

      print('Loaded reminder settings: ${settings.toJson()}');

      // Schedule notifications based on current settings
      if (settings.isPeriodReminderEnabled ||
          settings.isOvulationReminderEnabled) {
        print('Scheduling notifications during initialization...');
        final scheduleResult = await scheduleNotificationsForSettings(settings);

        scheduleResult.mapBoth(
          onLeft: (failure) {
            print(
                'Failed to schedule notifications during initialization: $failure');
          },
          onRight: (_) {
            print('Period reminders system initialized successfully');
          },
        );

        return scheduleResult;
      } else {
        print('No reminders enabled, initialization complete');
        return const Right(unit);
      }
    } catch (e) {
      print('Error initializing period reminders: $e');
      return const Left(PeriodTrackingFailure.unexpected());
    }
  }

  // Helper method to sync reminder settings from Firestore
  Future<void> _syncReminderSettingsFromFirestore() async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) return;

      final userDoc = await _firestore.collection('users').doc(user.uid).get();

      if (userDoc.exists &&
          userDoc.data()?.containsKey('reminderSettings') == true) {
        final firestoreSettings =
            userDoc.data()!['reminderSettings'] as Map<String, dynamic>;
        final settings = PeriodReminderSettings.fromJson(firestoreSettings);

        // Save to local storage
        final prefs = await SharedPreferences.getInstance();
        final settingsJson = jsonEncode(settings.toJson());
        await prefs.setString(_reminderSettingsKey, settingsJson);

        print('Synced reminder settings from Firestore to local storage');
      }
    } catch (e) {
      print('Error syncing reminder settings from Firestore: $e');
    }
  }
}
