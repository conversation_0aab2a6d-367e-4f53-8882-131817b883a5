import 'package:flutter_test/flutter_test.dart';
import 'package:account_management/domain/model/period_tracking_model.dart';
import 'package:account_management/domain/model/symptom_model.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

void main() {
  group('PeriodTrackingModel Symptoms Tests', () {
    test('should serialize and deserialize symptoms correctly', () {
      // Create a PeriodTrackingModel with symptoms
      final symptoms = [
        SymptomModel(name: 'Headache'),
        SymptomModel(name: 'Cramps'),
        SymptomModel(name: 'Bloating'),
      ];

      final originalModel = PeriodTrackingModel(
        date: DateTime(2024, 1, 15),
        symptoms: symptoms,
        painLevel: 5,
        flowLevel: 2,
      );

      // Convert to JSON and back
      final json = originalModel.toJson();
      final deserializedModel = PeriodTrackingModel.fromJson(json);

      // Verify symptoms are preserved
      expect(deserializedModel.symptoms, isNotNull);
      expect(deserializedModel.symptoms!.length, equals(3));
      expect(deserializedModel.symptoms![0].name, equals('Headache'));
      expect(deserializedModel.symptoms![1].name, equals('Cramps'));
      expect(deserializedModel.symptoms![2].name, equals('Bloating'));
      expect(deserializedModel.painLevel, equals(5));
      expect(deserializedModel.flowLevel, equals(2));
    });

    test('should handle empty symptoms list', () {
      final originalModel = PeriodTrackingModel(
        date: DateTime(2024, 1, 15),
        symptoms: [],
        painLevel: 3,
        flowLevel: 1,
      );

      final json = originalModel.toJson();
      final deserializedModel = PeriodTrackingModel.fromJson(json);

      expect(deserializedModel.symptoms, isNotNull);
      expect(deserializedModel.symptoms!.length, equals(0));
    });

    test('should create empty model with default values', () {
      final emptyModel = PeriodTrackingModel.empty();

      expect(emptyModel.symptoms, isNotNull);
      expect(emptyModel.symptoms!.length, equals(0));
      expect(emptyModel.painLevel, equals(0));
      expect(emptyModel.flowLevel, equals(0));
      expect(emptyModel.date, isNull);
    });

    test('should copy model with updated symptoms', () {
      final originalModel = PeriodTrackingModel(
        date: DateTime(2024, 1, 15),
        symptoms: [SymptomModel(name: 'Headache')],
        painLevel: 3,
        flowLevel: 1,
      );

      final newSymptoms = [
        SymptomModel(name: 'Headache'),
        SymptomModel(name: 'Fatigue'),
      ];

      final updatedModel = originalModel.copyWith(symptoms: newSymptoms);

      expect(updatedModel.symptoms!.length, equals(2));
      expect(updatedModel.symptoms![0].name, equals('Headache'));
      expect(updatedModel.symptoms![1].name, equals('Fatigue'));
      expect(updatedModel.painLevel, equals(3)); // Should preserve other fields
      expect(updatedModel.flowLevel, equals(1));
    });

    test('should handle Firestore Timestamp to String conversion', () {
      // Simulate data coming from Firestore with Timestamp
      final firestoreData = {
        'date': Timestamp.fromDate(DateTime(2024, 1, 15)),
        'symptoms': [
          {'name': 'Headache'},
          {'name': 'Cramps'},
        ],
        'painLevel': 7,
        'flowLevel': 2,
        'isSelected': true,
      };

      // Convert Timestamp to ISO8601 String (as our fix does)
      final processedData = Map<String, dynamic>.from(firestoreData);
      if (processedData['date'] is Timestamp) {
        processedData['date'] =
            (processedData['date'] as Timestamp).toDate().toIso8601String();
      }

      // Should now deserialize without errors
      final model = PeriodTrackingModel.fromJson(processedData);

      expect(model.date, equals(DateTime(2024, 1, 15)));
      expect(model.symptoms!.length, equals(2));
      expect(model.symptoms![0].name, equals('Headache'));
      expect(model.symptoms![1].name, equals('Cramps'));
      expect(model.painLevel, equals(7));
      expect(model.flowLevel, equals(2));
    });

    test('should ensure consistent Timestamp storage for symptom data', () {
      // Test the caching logic that converts symptom data to Firestore format
      final model = PeriodTrackingModel(
        date: DateTime(2024, 1, 15),
        symptoms: [SymptomModel(name: 'Fatigue')],
        painLevel: 4,
        flowLevel: 3,
      );

      // Simulate the _cacheSymptomChange logic
      final modelJson = model.toJson();

      // Convert date to Timestamp for consistency with period date storage
      if (modelJson['date'] is String) {
        final dateTime = DateTime.parse(modelJson['date'] as String);
        modelJson['date'] = Timestamp.fromDate(dateTime);
      }

      // Add isSelected field
      modelJson['isSelected'] = true;

      // Verify the date is now stored as Timestamp
      expect(modelJson['date'], isA<Timestamp>());
      expect((modelJson['date'] as Timestamp).toDate(), equals(DateTime(2024, 1, 15)));
      expect(modelJson['isSelected'], equals(true));
      expect(modelJson['symptoms'], isA<List>());
      expect((modelJson['symptoms'] as List).length, equals(1));
      expect(modelJson['painLevel'], equals(4));
      expect(modelJson['flowLevel'], equals(3));
    });
  });
}
