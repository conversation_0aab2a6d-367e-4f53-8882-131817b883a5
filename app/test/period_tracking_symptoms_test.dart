import 'package:flutter_test/flutter_test.dart';
import 'package:account_management/domain/model/period_tracking_model.dart';
import 'package:account_management/domain/model/symptom_model.dart';

void main() {
  group('PeriodTrackingModel Symptoms Tests', () {
    test('should serialize and deserialize symptoms correctly', () {
      // Create a PeriodTrackingModel with symptoms
      final symptoms = [
        SymptomModel(name: 'Headache'),
        SymptomModel(name: 'Cramps'),
        SymptomModel(name: 'Bloating'),
      ];
      
      final originalModel = PeriodTrackingModel(
        date: DateTime(2024, 1, 15),
        symptoms: symptoms,
        painLevel: 5,
        flowLevel: 2,
      );

      // Convert to JSON and back
      final json = originalModel.toJson();
      final deserializedModel = PeriodTrackingModel.fromJson(json);

      // Verify symptoms are preserved
      expect(deserializedModel.symptoms, isNotNull);
      expect(deserializedModel.symptoms!.length, equals(3));
      expect(deserializedModel.symptoms![0].name, equals('Headache'));
      expect(deserializedModel.symptoms![1].name, equals('Cramps'));
      expect(deserializedModel.symptoms![2].name, equals('Bloating'));
      expect(deserializedModel.painLevel, equals(5));
      expect(deserializedModel.flowLevel, equals(2));
    });

    test('should handle empty symptoms list', () {
      final originalModel = PeriodTrackingModel(
        date: DateTime(2024, 1, 15),
        symptoms: [],
        painLevel: 3,
        flowLevel: 1,
      );

      final json = originalModel.toJson();
      final deserializedModel = PeriodTrackingModel.fromJson(json);

      expect(deserializedModel.symptoms, isNotNull);
      expect(deserializedModel.symptoms!.length, equals(0));
    });

    test('should create empty model with default values', () {
      final emptyModel = PeriodTrackingModel.empty();
      
      expect(emptyModel.symptoms, isNotNull);
      expect(emptyModel.symptoms!.length, equals(0));
      expect(emptyModel.painLevel, equals(0));
      expect(emptyModel.flowLevel, equals(0));
      expect(emptyModel.date, isNull);
    });

    test('should copy model with updated symptoms', () {
      final originalModel = PeriodTrackingModel(
        date: DateTime(2024, 1, 15),
        symptoms: [SymptomModel(name: 'Headache')],
        painLevel: 3,
        flowLevel: 1,
      );

      final newSymptoms = [
        SymptomModel(name: 'Headache'),
        SymptomModel(name: 'Fatigue'),
      ];

      final updatedModel = originalModel.copyWith(symptoms: newSymptoms);

      expect(updatedModel.symptoms!.length, equals(2));
      expect(updatedModel.symptoms![0].name, equals('Headache'));
      expect(updatedModel.symptoms![1].name, equals('Fatigue'));
      expect(updatedModel.painLevel, equals(3)); // Should preserve other fields
      expect(updatedModel.flowLevel, equals(1));
    });
  });
}
