import 'dart:async';
import 'package:auto_route/auto_route.dart';
import 'package:bluetooth/application/bluetooth_service_bloc/bluetooth_service_bloc.dart';
import 'package:design_system/design/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:juno_plus/pages/remote/battery_indicator_widget.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:juno_plus/pages/remote/tens_mode.dart';
import 'package:juno_plus/pages/remote/tens_widget.dart';
import 'package:juno_plus/routing/app_pages.gr.dart';
import 'package:remote/application/device_control_bloc/device_control_bloc.dart';
import 'package:remote/application/device_control_heat_bloc/device_control_heat_bloc.dart';
import 'package:remote/application/device_control_heat_watcher_bloc/device_control_heat_watcher_bloc.dart';
import 'package:remote/application/device_control_tens_bloc/device_control_tens_bloc.dart';
import 'package:remote/application/device_control_tens_watcher_bloc/device_control_tens_watcher_bloc.dart';
import 'package:remote/application/device_status_watcher_bloc/device_status_watcher_bloc.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:showcaseview/showcaseview.dart';
import '../../helpers.dart';
import '../therapy/therapy_feedback_bottom_sheet.dart';
import 'heat_widget.dart';

//global variable to be deleted
bool _showCase = false;

@RoutePage()
class RemoteExperimentPage extends StatefulWidget {
  @override
  State<RemoteExperimentPage> createState() => _RemoteExperimentPageState();
}

class _RemoteExperimentPageState extends State<RemoteExperimentPage> {
  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<DeviceControlHeatBloc>(
          create: (context) => getIt<DeviceControlHeatBloc>(),
        ),
        BlocProvider<DeviceControlHeatWatcherBloc>(
          create: (context) => getIt<DeviceControlHeatWatcherBloc>()
            ..add(const DeviceControlHeatWatcherEvent.watchAllStarted()),
        ),
        BlocProvider<DeviceControlTensBloc>(
          create: (context) => getIt<DeviceControlTensBloc>(),
        ),
        BlocProvider<DeviceControlTensWatcherBloc>(
          create: (context) => getIt<DeviceControlTensWatcherBloc>()
            ..add(const DeviceControlTensWatcherEvent.watchAllStarted()),
        ),
        BlocProvider<DeviceStatusWatcherBloc>(
            create: (context) => getIt<DeviceStatusWatcherBloc>()
              ..add(const DeviceStatusWatcherEvent.watchAllStarted())),
        BlocProvider<BluetoothServiceBloc>(
          create: (context) => getIt<BluetoothServiceBloc>(),
        ),
        BlocProvider<DeviceControlBloc>(
            create: (context) => getIt<DeviceControlBloc>()),
      ],
      child: ShowCaseWidget(
        enableShowcase: false,
        builder: (context) {
          return RemoteScaffold();
        },
      ),
    );
  }
}

class RemoteScaffold extends StatefulWidget {
  const RemoteScaffold({super.key});

  @override
  State<RemoteScaffold> createState() => _RemoteScaffoldState();
}

class _RemoteScaffoldState extends State<RemoteScaffold> {
  final GlobalKey _tensExplainerKey = GlobalKey();
  final GlobalKey _heatExplainerKey = GlobalKey();
  final GlobalKey _syncButtonKey = GlobalKey();
  final GlobalKey _helpButtonKey = GlobalKey();
  final GlobalKey _settingsButtonKey = GlobalKey();
  final GlobalKey _modesExplainerKey = GlobalKey();
  final GlobalKey _playPauseExplainerKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    // _checkBinding();
  }

  Future<void> _checkBinding() async {
    if (!_showCase) {
      Future.delayed(Duration(milliseconds: 500), () {
        ShowCaseWidget.of(context).startShowCase([
          _tensExplainerKey,
          _modesExplainerKey,
          _heatExplainerKey,
          _syncButtonKey,
          _playPauseExplainerKey,
          _settingsButtonKey,
          _helpButtonKey
        ]);
      });

      _showCase = true;
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<DeviceControlBloc, DeviceControlState>(
        listener: (context, state) {
          // Listen for therapy state changes
          state.whenOrNull(
            therapyStateChanged: (isActive) {
              // Show feedback when therapy session ends (was active, now inactive)
              if (!isActive) {
                // Generate a session ID for this therapy session
                final sessionId =
                    DateTime.now().millisecondsSinceEpoch.toString();

                // Show feedback bottom sheet immediately when therapy ends
                TherapyFeedbackBottomSheet.show(context, sessionId);
              }
            },
          );
        },
        child: Container(
            color: Colors.white,
            child: SafeArea(
              bottom: false,
              child: Scaffold(
                appBar: PreferredSize(
                    preferredSize: Size.fromHeight(250.dg),
                    child: Container(
                      child: Padding(
                        padding: const EdgeInsets.only(
                            left: 25.0, right: 25.0, top: 10, bottom: 3),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            GestureDetector(
                              onTap: () {},
                              key: Key('settings_button'),
                              child: Container(
                                height: 50,
                                width: 50,
                                decoration: BoxDecoration(
                                  color: Color(0xffFAF2DF),
                                  shape: BoxShape.circle,
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.grey.withOpacity(0.6),
                                      offset: Offset(4, 4),
                                      blurRadius: 10,
                                    ),
                                  ],
                                ),
                                child: Padding(
                                  padding: const EdgeInsets.all(6.0),
                                  child: Icon(
                                    Icons.settings_outlined,
                                    color: Color(0xff30285D),
                                    size: 35,
                                  ),
                                ),
                              ),
                            ),
                            SizedBox(
                              width: 25,
                            ),
                            Align(
                              alignment: Alignment.center,
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    crossAxisAlignment: CrossAxisAlignment.end,
                                    children: [
                                      Container(
                                          width: 60,
                                          child: SvgPicture.asset(
                                            'assets/logo/juno_logo_violet.svg',
                                            height: 35,
                                            color: AppTheme.primaryColor,
                                            fit: BoxFit.fitWidth,
                                          )),
                                      SizedBox(
                                        width: 5,
                                      ),
                                      Text(
                                        '+',
                                        style: GoogleFonts.poppins(
                                          color: AppTheme.primaryColor,
                                          fontSize: 30,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ],
                                  ),
                                  Text(
                                    'remote',
                                    style: GoogleFonts.poppins(
                                      color: AppTheme.primaryColor,
                                      fontSize: 14,
                                      fontStyle: FontStyle.italic,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Row(
                              children: [
                                BatteryIndicatorWidget(),
                                SizedBox(
                                  width: 10,
                                ),
                                GestureDetector(
                                  onTap: () {
                                    context.router.push(HelpCenterHomeRoute());
                                  },
                                  child: Container(
                                    height: 50,
                                    width: 50,
                                    decoration: BoxDecoration(
                                      color: Color(0xffFAF2DF),
                                      shape: BoxShape.circle,
                                      boxShadow: [
                                        BoxShadow(
                                          color: Colors.grey.withOpacity(0.6),
                                          offset: Offset(4, 4),
                                          blurRadius: 10,
                                        ),
                                      ],
                                    ),
                                    child: Showcase(
                                      key: _helpButtonKey,
                                      title: 'Help',
                                      description:
                                          'Press the help button to access the help center.',
                                      descTextStyle: GoogleFonts.roboto(
                                        color: AppTheme.primaryColor,
                                        fontSize: 14,
                                        fontWeight: FontWeight.w400,
                                      ),
                                      tooltipBorderRadius:
                                          BorderRadius.circular(20),
                                      tooltipBackgroundColor: Color(0xffFAF2DF),
                                      tooltipPadding: EdgeInsets.all(20),
                                      titleTextStyle: GoogleFonts.roboto(
                                        color: AppTheme.primaryColor,
                                        fontSize: 20,
                                        fontWeight: FontWeight.w700,
                                      ),
                                      targetShapeBorder: CircleBorder(),
                                      child: Padding(
                                        padding: const EdgeInsets.all(6.0),
                                        child: Icon(
                                          Icons.help_outline_sharp,
                                          color: Color(0xff30285D),
                                          size: 35,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    )),
                body: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 20.0, vertical: 6),
                      child: Container(
                          width: .9.sw,
                          decoration: BoxDecoration(
                              boxShadow: [
                                BoxShadow(
                                  color: Color(
                                      0x40000000), // #00000040 in CSS corresponds to 0x40000000 in Flutter
                                  blurRadius: 4.0, // the blur radius
                                  offset: Offset(
                                      0, 1), // the x,y offset of the shadow
                                ),
                              ],
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(32)),
                          child: Padding(
                            padding: const EdgeInsets.all(6.0),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Showcase(
                                      key: _modesExplainerKey,
                                      title: 'Modes',
                                      description:
                                          'The TENS unit has 3 modes: Pulse, Constant, and Burst. Pulse mode delivers a series of electrical pulses in a pattern, Constant mode delivers a continuous electrical pulse, and Burst mode delivers a series of electrical pulses in rapid succession. Each mode provides a different type of pain relief, so you can choose the one that works best for you.',
                                      targetBorderRadius: BorderRadius.only(
                                        bottomLeft: Radius.circular(26),
                                      ),
                                      descTextStyle: GoogleFonts.roboto(
                                        color: AppTheme.primaryColor,
                                        fontSize: 14,
                                        fontWeight: FontWeight.w400,
                                      ),
                                      tooltipBorderRadius:
                                          BorderRadius.circular(20),
                                      tooltipBackgroundColor: Color(0xffFAF2DF),
                                      tooltipPadding: EdgeInsets.all(20),
                                      titleTextStyle: GoogleFonts.roboto(
                                        color: AppTheme.primaryColor,
                                        fontSize: 20,
                                        fontWeight: FontWeight.w700,
                                      ),
                                      child: Container(
                                        height: .125.sh,
                                        width: .5.sw - 13,
                                        decoration: BoxDecoration(
                                          color: Color(0xffFAF2DF),
                                          borderRadius: BorderRadius.only(
                                            topLeft: Radius.circular(26),
                                          ),
                                        ),
                                        child: Column(
                                          mainAxisAlignment:
                                              MainAxisAlignment.start,
                                          children: [
                                            SizedBox(
                                              height: 10,
                                            ),
                                            Row(
                                              children: [
                                                SizedBox(
                                                  width: .15.sw,
                                                ),
                                                Text(
                                                  'Modes',
                                                  style: Theme.of(context)
                                                      .textTheme
                                                      .bodyMedium!
                                                      .copyWith(
                                                          color:
                                                              Color(0xff26204A),
                                                          fontWeight:
                                                              FontWeight.w400,
                                                          fontSize: 16),
                                                ),
                                                SizedBox(
                                                  width: 10,
                                                ),
                                                GestureDetector(
                                                  onTap: () {
                                                    showDialog(
                                                      context: context,
                                                      builder: (BuildContext
                                                          context) {
                                                        return AlertDialog(
                                                          backgroundColor:
                                                              Color(0xffFAF2DF),
                                                          title: Text(
                                                            'Modes',
                                                            style: TextStyle(
                                                                color: Color(
                                                                    0xff26204A),
                                                                fontSize: 20,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .w500),
                                                          ),
                                                          content: Text(
                                                            'The TENS unit has 3 modes: Pulse, Constant, and Burst. Pulse mode delivers a series of electrical pulses in a pattern, Constant mode delivers a continuous electrical pulse, and Burst mode delivers a series of electrical pulses in rapid succession. Each mode provides a different type of pain relief, so you can choose the one that works best for you.',
                                                            style: TextStyle(
                                                                color: Color(
                                                                    0xff26204A),
                                                                fontSize: 16),
                                                          ),
                                                          actions: [
                                                            TextButton(
                                                              onPressed: () {
                                                                Navigator.of(
                                                                        context)
                                                                    .pop();
                                                              },
                                                              child: Text('OK'),
                                                            ),
                                                          ],
                                                        );
                                                      },
                                                    );
                                                  },
                                                  child: Icon(
                                                    Icons.info_outline,
                                                    size: 23,
                                                  ),
                                                ),
                                              ],
                                            ),
                                            SizedBox(
                                              height: 10,
                                            ),
                                            TensMode()
                                          ],
                                        ),
                                      ),
                                    ),
                                    Showcase(
                                      key: _syncButtonKey,
                                      title: 'Sync',
                                      description:
                                          'Press the sync button to manually sync the device with the app.',
                                      descTextStyle: GoogleFonts.roboto(
                                        color: AppTheme.primaryColor,
                                        fontSize: 14,
                                        fontWeight: FontWeight.w400,
                                      ),
                                      tooltipBorderRadius:
                                          BorderRadius.circular(20),
                                      tooltipBackgroundColor: Color(0xffFAF2DF),
                                      tooltipPadding: EdgeInsets.all(20),
                                      titleTextStyle: GoogleFonts.roboto(
                                        color: AppTheme.primaryColor,
                                        fontSize: 20,
                                        fontWeight: FontWeight.w700,
                                      ),
                                      targetBorderRadius: BorderRadius.only(
                                        topRight: Radius.circular(26),
                                      ),
                                      child: Container(
                                        height: .125.sh,
                                        width: .4.sw - 13,
                                        decoration: BoxDecoration(
                                            color: Color(0xffFAF2DF),
                                            borderRadius: BorderRadius.only(
                                              topRight: Radius.circular(26),
                                            )),
                                        child: Center(
                                            child: SyncButtonContainer()),
                                      ),
                                    ),
                                  ],
                                ),
                                SizedBox(
                                  height: .01.sh,
                                ),
                                Stack(
                                  alignment: Alignment.center,
                                  children: [
                                    Positioned(
                                      top: 0,
                                      left: 0,
                                      child: Showcase(
                                        key: _tensExplainerKey,
                                        targetBorderRadius: BorderRadius.only(
                                          bottomLeft: Radius.circular(32),
                                        ),
                                        title: 'TENS',
                                        description:
                                            'TENS (Transcutaneous Electrical Nerve Stimulation) uses gentle electrical pulses to help relieve pain and reduce muscle spasms. It has 10 intensity levels that can be adjusted using the + and - buttons. Please set the modes first before adjusting the intensity.',
                                        descTextStyle: GoogleFonts.roboto(
                                          color: AppTheme.primaryColor,
                                          fontSize: 14,
                                          fontWeight: FontWeight.w400,
                                        ),
                                        tooltipBorderRadius:
                                            BorderRadius.circular(20),
                                        tooltipBackgroundColor:
                                            Color(0xffFAF2DF),
                                        tooltipPadding: EdgeInsets.all(20),
                                        titleTextStyle: GoogleFonts.roboto(
                                          color: AppTheme.primaryColor,
                                          fontSize: 20,
                                          fontWeight: FontWeight.w700,
                                        ),
                                        child: Container(
                                          width: .5.sw - 13,
                                          height: .685.sh,
                                          decoration: BoxDecoration(
                                            borderRadius: BorderRadius.only(
                                              bottomLeft: Radius.circular(32),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                    Column(
                                      children: [
                                        Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            Container(
                                              width: .5.sw - 13,
                                              height: .55.sh,
                                              child: Column(
                                                mainAxisAlignment:
                                                    MainAxisAlignment.start,
                                                children: [
                                                  Container(
                                                    width: .48.sw,
                                                    height: .55.sh,
                                                    decoration: BoxDecoration(
                                                      color: Color(0xffFAF2DF),
                                                      borderRadius:
                                                          BorderRadius.only(
                                                        bottomLeft:
                                                            Radius.circular(26),
                                                      ),
                                                    ),
                                                    child: Column(
                                                        crossAxisAlignment:
                                                            CrossAxisAlignment
                                                                .center,
                                                        children: [
                                                          SizedBox(
                                                            height: 15,
                                                          ),
                                                          Row(
                                                            mainAxisAlignment:
                                                                MainAxisAlignment
                                                                    .center,
                                                            children: [
                                                              SizedBox(
                                                                width: .11.sw,
                                                              ),
                                                              SvgPicture.asset(
                                                                'assets/remote/remote_charge.svg',
                                                                width: 23,
                                                                height: 23,
                                                              ),
                                                              SizedBox(
                                                                width: 10,
                                                              ),
                                                              GestureDetector(
                                                                onTap: () {
                                                                  showDialog(
                                                                    context:
                                                                        context,
                                                                    builder:
                                                                        (BuildContext
                                                                            context) {
                                                                      return AlertDialog(
                                                                        backgroundColor:
                                                                            Color(0xffFAF2DF),
                                                                        title:
                                                                            Text(
                                                                          'TENS',
                                                                          style: TextStyle(
                                                                              color: Color(0xff26204A),
                                                                              fontSize: 20,
                                                                              fontWeight: FontWeight.w500),
                                                                        ),
                                                                        content:
                                                                            Text(
                                                                          'TENS (Transcutaneous Electrical Nerve Stimulation) is a type of therapy that uses gentle electrical pulses to help relieve pain and reduce muscle spasms. With 10 adjustable intensity levels, TENS can be customized to your comfort and needs, providing targeted relief right where you need it.',
                                                                          style: TextStyle(
                                                                              color: Color(0xff26204A),
                                                                              fontSize: 16),
                                                                        ),
                                                                        actions: [
                                                                          TextButton(
                                                                            onPressed:
                                                                                () {
                                                                              Navigator.of(context).pop();
                                                                            },
                                                                            child:
                                                                                Text('OK'),
                                                                          ),
                                                                        ],
                                                                      );
                                                                    },
                                                                  );
                                                                },
                                                                child: Icon(
                                                                  Icons
                                                                      .info_outline,
                                                                  size: 23,
                                                                ),
                                                              ),
                                                            ],
                                                          ),
                                                          RichText(
                                                              text: TextSpan(
                                                                  text: 'TENS ',
                                                                  style: Theme.of(
                                                                          context)
                                                                      .textTheme
                                                                      .bodyMedium!
                                                                      .copyWith(
                                                                          color: Color(
                                                                              0xff26204A),
                                                                          fontWeight: FontWeight
                                                                              .w400,
                                                                          fontSize:
                                                                              20),
                                                                  children: [
                                                                TextSpan(
                                                                    text:
                                                                        'Intensity',
                                                                    style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                                                                        fontStyle:
                                                                            FontStyle
                                                                                .italic,
                                                                        color: Color(
                                                                            0xff26204A),
                                                                        fontWeight:
                                                                            FontWeight
                                                                                .w400,
                                                                        fontSize:
                                                                            16))
                                                              ])),
                                                          SizedBox(
                                                            height: 10,
                                                          ),
                                                          TensWidget(),
                                                          SizedBox(
                                                            height: 15,
                                                          ),
                                                        ]),
                                                  ),
                                                ],
                                              ),
                                            ),
                                            Container(
                                              width: .4.sw - 13,
                                              height: .55.sh,
                                              decoration: BoxDecoration(
                                                color: Colors.white,
                                                borderRadius: BorderRadius.only(
                                                  bottomRight:
                                                      Radius.circular(26),
                                                ),
                                              ),
                                              child: Column(
                                                mainAxisAlignment:
                                                    MainAxisAlignment
                                                        .spaceBetween,
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.center,
                                                children: [
                                                  Showcase(
                                                    key: _heatExplainerKey,
                                                    title: 'Heat',
                                                    description:
                                                        'Heat therapy is a type of treatment that uses heat to relieve pain and stiffness. With 3 adjustable levels, heat therapy can be customized to your comfort and needs, providing targeted relief right where you need it.',
                                                    descTextStyle:
                                                        GoogleFonts.roboto(
                                                      color:
                                                          AppTheme.primaryColor,
                                                      fontSize: 14,
                                                      fontWeight:
                                                          FontWeight.w400,
                                                    ),
                                                    tooltipBorderRadius:
                                                        BorderRadius.circular(
                                                            20),
                                                    tooltipBackgroundColor:
                                                        Color(0xffFAF2DF),
                                                    tooltipPadding:
                                                        EdgeInsets.all(20),
                                                    titleTextStyle:
                                                        GoogleFonts.roboto(
                                                      color:
                                                          AppTheme.primaryColor,
                                                      fontSize: 20,
                                                      fontWeight:
                                                          FontWeight.w700,
                                                    ),
                                                    child: Container(
                                                      width: .4.sw - 13,
                                                      height: .415.sh,
                                                      decoration: BoxDecoration(
                                                        color: Color(0xffFAF2DF)
                                                            .withOpacity(.4),
                                                      ),
                                                      child: Column(
                                                        children: [
                                                          SizedBox(
                                                            height: 15,
                                                          ),
                                                          Row(
                                                            children: [
                                                              SizedBox(
                                                                width: .16.sw,
                                                              ),
                                                              SvgPicture.asset(
                                                                'assets/remote/remote_heat.svg',
                                                                width: 23,
                                                                height: 23,
                                                              ),
                                                              SizedBox(
                                                                width: 10,
                                                              ),
                                                              GestureDetector(
                                                                onTap: () {
                                                                  showDialog(
                                                                    context:
                                                                        context,
                                                                    builder:
                                                                        (BuildContext
                                                                            context) {
                                                                      return AlertDialog(
                                                                        backgroundColor:
                                                                            Color(0xffFAF2DF),
                                                                        title:
                                                                            Text(
                                                                          'Heat',
                                                                          style: TextStyle(
                                                                              color: Color(0xff26204A),
                                                                              fontSize: 20,
                                                                              fontWeight: FontWeight.w500),
                                                                        ),
                                                                        content:
                                                                            Text(
                                                                          'Heat therapy is a type of treatment that uses heat to relieve pain and stiffness. With 3 adjustable levels, heat therapy can be customized to your comfort and needs, providing targeted relief right where you need it.',
                                                                          style: TextStyle(
                                                                              color: Color(0xff26204A),
                                                                              fontSize: 16),
                                                                        ),
                                                                        actions: [
                                                                          TextButton(
                                                                            onPressed:
                                                                                () {
                                                                              Navigator.of(context).pop();
                                                                            },
                                                                            child:
                                                                                Text('OK'),
                                                                          ),
                                                                        ],
                                                                      );
                                                                    },
                                                                  );
                                                                },
                                                                child: Icon(
                                                                  Icons
                                                                      .info_outline,
                                                                  size: 23,
                                                                ),
                                                              ),
                                                            ],
                                                          ),
                                                          SizedBox(
                                                            height: 10,
                                                          ),
                                                          Text(
                                                            'Heat',
                                                            style: Theme.of(
                                                                    context)
                                                                .textTheme
                                                                .bodyMedium!
                                                                .copyWith(
                                                                    color: Color(
                                                                        0xff26204A),
                                                                    fontWeight:
                                                                        FontWeight
                                                                            .w400,
                                                                    fontSize:
                                                                        20),
                                                          ),
                                                          SizedBox(
                                                            height: 10,
                                                          ),
                                                          Container(
                                                              child:
                                                                  CustomProgressWidget()),
                                                        ],
                                                      ),
                                                    ),
                                                  ),
                                                  Showcase(
                                                    key: _playPauseExplainerKey,
                                                    title: 'Play/Pause',
                                                    description:
                                                        'You can pause and resume the therapy session by pressing the play/pause button.',
                                                    descTextStyle:
                                                        GoogleFonts.roboto(
                                                      color:
                                                          AppTheme.primaryColor,
                                                      fontSize: 14,
                                                      fontWeight:
                                                          FontWeight.w400,
                                                    ),
                                                    tooltipBorderRadius:
                                                        BorderRadius.circular(
                                                            20),
                                                    tooltipBackgroundColor:
                                                        Color(0xffFAF2DF),
                                                    tooltipPadding:
                                                        EdgeInsets.all(20),
                                                    titleTextStyle:
                                                        GoogleFonts.roboto(
                                                      color:
                                                          AppTheme.primaryColor,
                                                      fontSize: 20,
                                                      fontWeight:
                                                          FontWeight.w700,
                                                    ),
                                                    child: Container(
                                                      width: .4.sw - 13,
                                                      height: .125.sh,
                                                      decoration: BoxDecoration(
                                                        color:
                                                            Color(0xffFAF2DF),
                                                        borderRadius:
                                                            BorderRadius.only(
                                                          bottomRight:
                                                              Radius.circular(
                                                                  26),
                                                        ),
                                                      ),
                                                      child: Center(
                                                        child: Container(
                                                          decoration:
                                                              BoxDecoration(
                                                            color: Colors.white,
                                                            borderRadius:
                                                                BorderRadius
                                                                    .circular(
                                                                        50),
                                                            boxShadow: [
                                                              BoxShadow(
                                                                color: Colors
                                                                    .grey
                                                                    .withOpacity(
                                                                        0.6),
                                                                offset: Offset(
                                                                    4, 4),
                                                                blurRadius: 10,
                                                              ),
                                                              // BoxShadow(
                                                              //   color: Colors.white,
                                                              //   offset: Offset(-4, -4),
                                                              //   blurRadius: 10,
                                                              // ),
                                                            ],
                                                          ),
                                                          child: IconButton(
                                                            icon: Icon(
                                                              Icons
                                                                  .play_circle_outline_rounded,
                                                              size: 50,
                                                            ),
                                                            onPressed: () {
                                                              BlocProvider.of<
                                                                          DeviceControlBloc>(
                                                                      context)
                                                                  .add(const DeviceControlEvent
                                                                      .toggleTherapy());
                                                            },
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  ],
                                )
                              ],
                            ),
                          )),
                    ),
                  ],
                ),
              ),
            )));
  }
}

class SyncButtonContainer extends StatefulWidget {
  @override
  _SyncButtonContainerState createState() => _SyncButtonContainerState();
}

class _SyncButtonContainerState extends State<SyncButtonContainer> {
  bool _isSyncing = false;

  void _startSync() {
    setState(() {
      _isSyncing = true;
    });

    // Revert back to the button after 4 seconds
    Timer(Duration(seconds: 4), () {
      setState(() {
        _isSyncing = false;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 0.125.sh,
      width: 0.4.sw - 13,
      decoration: BoxDecoration(
        color: Color(0xffFAF2DF),
        borderRadius: BorderRadius.only(
          topRight: Radius.circular(26),
        ),
      ),
      child: Center(
        child: AnimatedSwitcher(
          duration: Duration(milliseconds: 600),
          transitionBuilder: (Widget child, Animation<double> animation) {
            return FadeTransition(opacity: animation, child: child);
          },
          child: _isSyncing
              ? Text(
                  'Syncing...',
                  key: ValueKey<int>(1),
                  style: TextStyle(fontSize: 24, color: Colors.black),
                )
              : Container(
                  key: ValueKey<int>(2),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(50),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withOpacity(0.6),
                        offset: Offset(4, 4),
                        blurRadius: 10,
                      ),
                    ],
                  ),
                  child: IconButton(
                    icon: Icon(
                      Icons.sync,
                      size: 50,
                    ),
                    onPressed: _startSync,
                  ),
                ),
        ),
      ),
    );
  }
}
