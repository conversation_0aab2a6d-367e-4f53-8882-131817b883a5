import 'package:auto_route/auto_route.dart';
import 'package:design_system/design/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:juno_plus/routing/app_pages.gr.dart';
import 'package:lottie/lottie.dart';

import '../../custom_widgets/curved_app_bar.dart';

/// The SearchLoader class is a StatelessWidget that displays a loader while searching for devices.
class SearchLoader extends StatelessWidget {
  const SearchLoader({super.key});

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      child: Scaffold(
        backgroundColor: Color(0xffFAF2DF),
        appBar: CurvedAppBar(
          appBarColor: AppTheme.primaryColor,
          logoColor: Color(0xffFAF2DF),
          height: .35.sw, // The height of your curved app bar
        ),
        body: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Lottie.asset('assets/remote/loader.json', width: 1.sw, height: 400),
            SizedBox(
              height: 20,
            ),
            Text(
              'Searching for devices....',
              style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                  fontSize: 20,
                  color: Color(0xff26204a),
                  fontWeight: FontWeight.w300),
            ),
            SizedBox(
              height: 20,
            ),
            
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 44.0),
              child: GestureDetector(
                onTap: () {
                  context.router.push(TroubleshootingRoute());
                },
                child: Container(
                  width: .4.sw,
                  height: .12.sw,
                  decoration: BoxDecoration(
                    border: Border.all(color: Color(0xffBEBADE), width: 1),
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(25),
                      topRight: Radius.circular(25),
                      bottomLeft: Radius.circular(25),
                      bottomRight: Radius.circular(25),
                    ),
                    color: AppTheme.primaryColor,
                  ),
                  child: Center(
                      child: Text(
                    "Help",
                    style: Theme.of(context)
                        .textTheme
                        .bodySmall!
                        .copyWith(fontSize: 20, color: Colors.white),
                  )),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
