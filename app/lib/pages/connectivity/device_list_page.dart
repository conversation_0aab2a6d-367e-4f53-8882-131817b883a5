import 'package:bluetooth/bluetooth.dart';
import 'package:design_system/design_system.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../custom_widgets/curved_app_bar.dart';

/// The DeviceListPage class is a StatelessWidget that displays a list of devices that are available to connect to.
class DeviceListPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<BluetoothServiceBloc, BluetoothServiceState>(
      builder: (context, state) {
        if (state is BluetoothAvailableTypeDevices) {
          return Scaffold(
              extendBodyBehindAppBar: true,
              appBar: CurvedAppBar(
                appBarColor: AppTheme.primaryColor,
                logoColor: Color(0xffFAF2DF),
                height: .35.sw, // The height of your curved app bar
                topLeftIcon: IconButton(
                  icon: Icon(Icons.arrow_back, color: Colors.white, size: 30),
                  onPressed: () {
                    // Navigate back to landing page
                    context
                        .read<BluetoothServiceBloc>()
                        .add(const MoveToLandingPage());
                  },
                ),
                topRightIcon: IconButton(
                  icon: Text(
                    "Scan",
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      color: Colors.white,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  onPressed: () {
                    context
                        .read<BluetoothServiceBloc>()
                        .add(StartSearch(false));
                  },
                ),
              ),
              body: Padding(
                padding: const EdgeInsets.only(top: 40.0, left: 20, right: 15),
                child: ListView.builder(
                  itemCount: state.devices!.length,
                  itemBuilder: (context, index) {
                    final device = state.devices![index]!;
                    return Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Container(
                        height: .15.sw,
                        decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.all(Radius.circular(15)),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.grey.withOpacity(0.2),
                                spreadRadius: 5,
                                blurRadius: 4,
                                offset:
                                    Offset(0, 4), // changes position of shadow
                              ),
                            ]),
                        child: ListTile(
                            title: Text(
                              device.platformName,
                              style: GoogleFonts.poppins(
                                fontSize: 18,
                                color: Colors.black,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                            leading: Image.asset(
                                'assets/remote/device_pair.png',
                                height: 100,
                                width: 100,
                                fit: BoxFit.fitWidth),
                            onTap: () {
                              print("device====================");
                              context.read<BluetoothServiceBloc>().add(
                                  ConnectToDevice(device.remoteId.str,
                                      initial: true));
                            },
                            trailing: Text(
                              "Connect",
                              style: GoogleFonts.poppins(
                                fontSize: 16,
                                color: AppTheme.primaryColor,
                                fontWeight: FontWeight.w600,
                              ),
                            )),
                      ),
                    );
                  },
                ),
              )); // Build your UI with the devices
        } else {
          return Container(); // Handle other states if needed
        }
      },
    );
  }
}
