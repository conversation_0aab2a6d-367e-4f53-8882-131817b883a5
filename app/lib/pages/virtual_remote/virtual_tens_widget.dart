import 'package:design_system/design/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'dart:async';
import 'virtual_sync_button.dart';

// Global state for mode changes
class VirtualTensModeState {
  static Function? _onModeChanged;

  static void setModeChangeCallback(Function callback) {
    _onModeChanged = callback;
  }

  static void notifyModeChanged() {
    _onModeChanged?.call();
  }
}

class VirtualTensWidget extends StatefulWidget {
  const VirtualTensWidget({Key? key}) : super(key: key);

  @override
  State<VirtualTensWidget> createState() => _VirtualTensWidgetState();
}

class _VirtualTensWidgetState extends State<VirtualTensWidget>
    with TickerProviderStateMixin {
  late AnimationController _blinkController;
  bool _showBlink = false;
  int _selectedTensLevel = 0; // Selected level (white indicators)
  int _actualTensLevel = 0; // Applied level (purple indicators)
  int? _applyingLevel; // Currently applying level (blinking)
  Timer? _applyTimer;
  int _previousSelectedLevel = 0; // Store previous level for play resume

  @override
  void initState() {
    super.initState();
    _blinkController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    )..addStatusListener((status) {
        if (status == AnimationStatus.completed) {
          setState(() {
            _showBlink = !_showBlink;
          });
          _blinkController.reset();
          _blinkController.forward();
        }
      });
    _blinkController.forward();

    // Listen for play/pause changes
    VirtualTherapyState.addPlayPauseCallback(_onPlayPauseChanged);

    // Listen for mode changes
    VirtualTensModeState.setModeChangeCallback(_onModeChanged);
  }

  void _onPlayPauseChanged(bool isPlaying) {
    print('TENS: Play/Pause changed to: $isPlaying');
    if (isPlaying) {
      // Resume: restore previous levels
      print('TENS: Resuming to level $_previousSelectedLevel');
      setState(() {
        _selectedTensLevel = _previousSelectedLevel;
      });
      _startApplyingLevels();
    } else {
      // Pause: reset all levels to 0
      print('TENS: Pausing, storing level $_selectedTensLevel');
      _applyTimer?.cancel();
      setState(() {
        _previousSelectedLevel = _selectedTensLevel; // Store current selection
        _selectedTensLevel = 0;
        _actualTensLevel = 0;
        _applyingLevel = null;
      });
    }
  }

  void _onModeChanged() {
    // Mode changed: reset TENS levels to 0
    _applyTimer?.cancel();
    setState(() {
      _selectedTensLevel = 0;
      _actualTensLevel = 0;
      _applyingLevel = null;
      _previousSelectedLevel = 0;
    });
  }

  @override
  void dispose() {
    _blinkController.dispose();
    _applyTimer?.cancel();
    VirtualTherapyState.removePlayPauseCallback(_onPlayPauseChanged);
    super.dispose();
  }

  void _startApplyingLevels() {
    _applyTimer?.cancel(); // Cancel any existing timer

    if (_selectedTensLevel == _actualTensLevel) {
      setState(() {
        _applyingLevel = null;
      });
      return;
    }

    if (_selectedTensLevel > _actualTensLevel) {
      // Increasing levels
      setState(() {
        _applyingLevel = _actualTensLevel;
      });

      _applyTimer = Timer(Duration(seconds: 5), () {
        setState(() {
          _actualTensLevel++;
          if (_actualTensLevel < _selectedTensLevel) {
            _startApplyingLevels(); // Continue applying next level
          } else {
            _applyingLevel = null; // All levels applied
          }
        });
      });
    } else {
      // Decreasing levels
      setState(() {
        _applyingLevel = _actualTensLevel - 1;
      });

      _applyTimer = Timer(Duration(seconds: 5), () {
        setState(() {
          _actualTensLevel--;
          if (_actualTensLevel > _selectedTensLevel) {
            _startApplyingLevels(); // Continue applying next level
          } else {
            _applyingLevel = null; // All levels applied
          }
        });
      });
    }
  }

  void _increaseTensLevel() {
    if (_selectedTensLevel < 10) {
      setState(() {
        _selectedTensLevel++;
      });
      _startApplyingLevels();
    }
  }

  void _decreaseTensLevel() {
    if (_selectedTensLevel > 0) {
      setState(() {
        _selectedTensLevel--;
      });
      _startApplyingLevels();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Increase Button
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(30),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.6),
                  offset: Offset(4, 4),
                  blurRadius: 10,
                ),
              ],
            ),
            child: IconButton(
              key: const Key('virtual_tens_increase_button'),
              icon: Icon(
                Icons.add,
                size: 28,
              ),
              onPressed: _increaseTensLevel,
            ),
          ),
          SizedBox(height: 8),
          // Tens Level Indicator
          Container(
            width: 35,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(30),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.6),
                  offset: Offset(4, 4),
                  blurRadius: 10,
                ),
                BoxShadow(
                  color: Colors.white,
                  offset: Offset(-4, -4),
                  blurRadius: 10,
                ),
              ],
            ),
            child: Padding(
              padding: const EdgeInsets.all(5.0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.end,
                children: List.generate(10, (index) {
                  int levelIndex = 9 - index;

                  // Determine color based on the actual, applying, and selected Tens levels
                  Color color;

                  if (levelIndex < _actualTensLevel &&
                      levelIndex != _applyingLevel) {
                    // Already applied level - purple
                    color = AppTheme.primaryColor;
                  } else if (_applyingLevel != null &&
                      levelIndex == _applyingLevel) {
                    // Currently applying level - blinking red
                    color = _showBlink
                        ? Theme.of(context).colorScheme.primary.withOpacity(0.6)
                        : Theme.of(context)
                            .colorScheme
                            .primary
                            .withOpacity(0.2);
                  } else if (levelIndex < _selectedTensLevel) {
                    // Selected but not yet applied - white
                    color = Colors.white;
                  } else {
                    // Inactive level - grey
                    color = Colors.grey[300]!;
                  }

                  return Container(
                    height: 64.dg,
                    margin: EdgeInsets.symmetric(vertical: 1),
                    decoration: BoxDecoration(
                      color: color,
                      borderRadius: BorderRadius.only(
                        topLeft:
                            levelIndex == 9 ? Radius.circular(30) : Radius.zero,
                        topRight:
                            levelIndex == 9 ? Radius.circular(30) : Radius.zero,
                        bottomLeft:
                            levelIndex == 0 ? Radius.circular(30) : Radius.zero,
                        bottomRight:
                            levelIndex == 0 ? Radius.circular(30) : Radius.zero,
                      ),
                    ),
                  );
                }),
              ),
            ),
          ),
          SizedBox(height: 8),
          // Decrease Button
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(30),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.6),
                  offset: Offset(4, 4),
                  blurRadius: 10,
                ),
              ],
            ),
            child: IconButton(
              key: const Key('virtual_tens_decrease_button'),
              icon: Icon(
                Icons.remove,
                size: 28,
              ),
              onPressed: _decreaseTensLevel,
            ),
          ),
        ],
      ),
    );
  }
}
