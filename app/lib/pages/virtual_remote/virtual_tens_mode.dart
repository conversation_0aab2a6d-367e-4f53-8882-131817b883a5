import 'package:flutter/material.dart';
import 'virtual_tens_widget.dart';

class VirtualTensMode extends StatefulWidget {
  const VirtualTensMode({super.key});

  @override
  State<VirtualTensMode> createState() => _VirtualTensModeState();
}

class _VirtualTensModeState extends State<VirtualTensMode> {
  int _selectedMode = 1; // Default to mode 1

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GestureDetector(
          onTap: () {
            setState(() {
              _selectedMode = 1;
            });
            // Notify TENS widget that mode changed
            VirtualTensModeState.notifyModeChanged();
          },
          key: Key('virtual_tens_mode_1'),
          child: Container(
            height: 40,
            width: 40,
            decoration: BoxDecoration(
              color: _selectedMode == 1 ? Color(0xffF7A600) : Colors.white,
              borderRadius: BorderRadius.circular(15),
              boxShadow: [
                BoxShadow(
                  color: Color(0x40000000),
                  blurRadius: 4.0,
                  offset: Offset(0, 1),
                ),
              ],
            ),
            child: Center(
                child: Text(
              '1',
              style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                  color: _selectedMode == 1 ? Colors.white : Color(0xff26204A),
                  fontWeight: FontWeight.w400,
                  fontSize: 16),
            )),
          ),
        ),
        SizedBox(width: 10),
        GestureDetector(
          onTap: () {
            setState(() {
              _selectedMode = 2;
            });
            // Notify TENS widget that mode changed
            VirtualTensModeState.notifyModeChanged();
          },
          key: Key('virtual_tens_mode_2'),
          child: Container(
            height: 40,
            width: 40,
            decoration: BoxDecoration(
              color: _selectedMode == 2 ? Color(0xffF7A600) : Colors.white,
              borderRadius: BorderRadius.circular(15),
              boxShadow: [
                BoxShadow(
                  color: Color(0x40000000),
                  blurRadius: 4.0,
                  offset: Offset(0, 1),
                ),
              ],
            ),
            child: Center(
                child: Text(
              '2',
              style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                  color: _selectedMode == 2 ? Colors.white : Color(0xff26204A),
                  fontWeight: FontWeight.w400,
                  fontSize: 16),
            )),
          ),
        ),
        SizedBox(width: 10),
        GestureDetector(
          onTap: () {
            setState(() {
              _selectedMode = 3;
            });
            // Notify TENS widget that mode changed
            VirtualTensModeState.notifyModeChanged();
          },
          key: Key('virtual_tens_mode_3'),
          child: Container(
            height: 40,
            width: 40,
            decoration: BoxDecoration(
              color: _selectedMode == 3 ? Color(0xffF7A600) : Colors.white,
              borderRadius: BorderRadius.circular(15),
              boxShadow: [
                BoxShadow(
                  color: Color(0x40000000),
                  blurRadius: 4.0,
                  offset: Offset(0, 1),
                ),
              ],
            ),
            child: Center(
                child: Text(
              '3',
              style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                  color: _selectedMode == 3 ? Colors.white : Color(0xff26204A),
                  fontWeight: FontWeight.w400,
                  fontSize: 16),
            )),
          ),
        ),
      ],
    );
  }
}
