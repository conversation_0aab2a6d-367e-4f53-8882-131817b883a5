import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:design_system/design/theme.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'dart:async';

class VirtualSyncButtonContainer extends StatefulWidget {
  const VirtualSyncButtonContainer({Key? key}) : super(key: key);

  @override
  State<VirtualSyncButtonContainer> createState() =>
      _VirtualSyncButtonContainerState();
}

class _VirtualSyncButtonContainerState
    extends State<VirtualSyncButtonContainer> {
  bool _isSyncing = false;

  void _startSync() {
    setState(() {
      _isSyncing = true;
    });

    // Revert back to the button after 4 seconds
    Timer(Duration(seconds: 4), () {
      setState(() {
        _isSyncing = false;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 0.125.sh,
      width: 0.4.sw - 13,
      decoration: BoxDecoration(
        color: Color(0xffFAF2DF),
        borderRadius: BorderRadius.only(
          topRight: Radius.circular(26),
        ),
      ),
      child: Center(
        child: AnimatedSwitcher(
          duration: Duration(milliseconds: 600),
          transitionBuilder: (Widget child, Animation<double> animation) {
            return FadeTransition(opacity: animation, child: child);
          },
          child: _isSyncing
              ? Text(
                  'Syncing...',
                  key: ValueKey<int>(1),
                  style: TextStyle(fontSize: 24, color: Colors.black),
                )
              : Container(
                  key: ValueKey<int>(2),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(50),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withOpacity(0.6),
                        offset: Offset(4, 4),
                        blurRadius: 10,
                      ),
                    ],
                  ),
                  child: IconButton(
                    icon: Icon(
                      Icons.sync,
                      size: 50,
                    ),
                    onPressed: _startSync,
                  ),
                ),
        ),
      ),
    );
  }
}

// Global state for play/pause
class VirtualTherapyState {
  static bool _isPlaying = false;
  static List<Function> _callbacks = [];

  static bool get isPlaying => _isPlaying;

  static void addPlayPauseCallback(Function callback) {
    _callbacks.add(callback);
  }

  static void removePlayPauseCallback(Function callback) {
    _callbacks.remove(callback);
  }

  static void togglePlayPause() {
    _isPlaying = !_isPlaying;
    print(
        'VirtualTherapyState: Toggled to $_isPlaying, notifying ${_callbacks.length} callbacks');
    // Notify all registered callbacks
    for (var callback in _callbacks) {
      callback(_isPlaying);
    }
  }
}

class VirtualPlayPauseButton extends StatefulWidget {
  const VirtualPlayPauseButton({Key? key}) : super(key: key);

  @override
  State<VirtualPlayPauseButton> createState() => _VirtualPlayPauseButtonState();
}

class _VirtualPlayPauseButtonState extends State<VirtualPlayPauseButton> {
  bool _isPlaying = false;

  @override
  void initState() {
    super.initState();
    _isPlaying = VirtualTherapyState.isPlaying;
  }

  void _togglePlayPause() {
    VirtualTherapyState.togglePlayPause();
    setState(() {
      _isPlaying = VirtualTherapyState.isPlaying;
    });

    Fluttertoast.showToast(
      msg: _isPlaying ? "Virtual therapy started" : "Virtual therapy paused",
      toastLength: Toast.LENGTH_SHORT,
      gravity: ToastGravity.BOTTOM,
      backgroundColor: AppTheme.primaryColor,
      textColor: Colors.white,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: .4.sw - 13,
      height: .125.sh,
      decoration: BoxDecoration(
        color: Color(0xffFAF2DF),
        borderRadius: BorderRadius.only(
          bottomRight: Radius.circular(26),
        ),
      ),
      child: Center(
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(50),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.6),
                offset: Offset(4, 4),
                blurRadius: 10,
              ),
            ],
          ),
          child: IconButton(
            icon: Icon(
              _isPlaying
                  ? Icons.pause_circle_outline_rounded
                  : Icons.play_circle_outline_rounded,
              size: 50,
            ),
            onPressed: _togglePlayPause,
          ),
        ),
      ),
    );
  }
}
