import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:design_system/design/theme.dart';
import 'dart:async';
import 'virtual_sync_button.dart';

class VirtualHeatWidget extends StatefulWidget {
  const VirtualHeatWidget({Key? key}) : super(key: key);

  @override
  State<VirtualHeatWidget> createState() => _VirtualHeatWidgetState();
}

class _VirtualHeatWidgetState extends State<VirtualHeatWidget>
    with TickerProviderStateMixin {
  late AnimationController _blinkController;
  bool _showBlink = false;
  int _selectedHeatLevel = 0; // Selected level (white indicators)
  int _actualHeatLevel = 0; // Applied level (orange indicators)
  int? _applyingLevel; // Currently applying level (blinking)
  Timer? _applyTimer;
  int _previousSelectedLevel = 0; // Store previous level for play resume

  @override
  void initState() {
    super.initState();
    _blinkController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    )..addStatusListener((status) {
        if (status == AnimationStatus.completed) {
          setState(() {
            _showBlink = !_showBlink;
          });
          _blinkController.reset();
          _blinkController.forward();
        }
      });
    _blinkController.forward();

    // Listen for play/pause changes
    VirtualTherapyState.addPlayPauseCallback(_onPlayPauseChanged);
  }

  void _onPlayPauseChanged(bool isPlaying) {
    print('HEAT: Play/Pause changed to: $isPlaying');
    if (isPlaying) {
      // Resume: restore previous levels
      print('HEAT: Resuming to level $_previousSelectedLevel');
      setState(() {
        _selectedHeatLevel = _previousSelectedLevel;
      });
      _startApplyingLevels();
    } else {
      // Pause: reset all levels to 0
      print('HEAT: Pausing, storing level $_selectedHeatLevel');
      _applyTimer?.cancel();
      setState(() {
        _previousSelectedLevel = _selectedHeatLevel; // Store current selection
        _selectedHeatLevel = 0;
        _actualHeatLevel = 0;
        _applyingLevel = null;
      });
    }
  }

  @override
  void dispose() {
    _blinkController.dispose();
    _applyTimer?.cancel();
    VirtualTherapyState.removePlayPauseCallback(_onPlayPauseChanged);
    super.dispose();
  }

  void _startApplyingLevels() {
    _applyTimer?.cancel(); // Cancel any existing timer

    if (_selectedHeatLevel == _actualHeatLevel) {
      setState(() {
        _applyingLevel = null;
      });
      return;
    }

    if (_selectedHeatLevel > _actualHeatLevel) {
      // Increasing levels
      setState(() {
        _applyingLevel = _actualHeatLevel;
      });

      _applyTimer = Timer(Duration(seconds: 5), () {
        setState(() {
          _actualHeatLevel++;
          if (_actualHeatLevel < _selectedHeatLevel) {
            _startApplyingLevels(); // Continue applying next level
          } else {
            _applyingLevel = null; // All levels applied
          }
        });
      });
    } else {
      // Decreasing levels
      setState(() {
        _applyingLevel = _actualHeatLevel - 1;
      });

      _applyTimer = Timer(Duration(seconds: 5), () {
        setState(() {
          _actualHeatLevel--;
          if (_actualHeatLevel > _selectedHeatLevel) {
            _startApplyingLevels(); // Continue applying next level
          } else {
            _applyingLevel = null; // All levels applied
          }
        });
      });
    }
  }

  void _increaseHeatLevel() {
    if (_selectedHeatLevel < 3) {
      setState(() {
        _selectedHeatLevel++;
      });
      _startApplyingLevels();
    }
  }

  void _decreaseHeatLevel() {
    if (_selectedHeatLevel > 0) {
      setState(() {
        _selectedHeatLevel--;
      });
      _startApplyingLevels();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Increase Button
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(30),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.6),
                  offset: Offset(4, 4),
                  blurRadius: 10,
                ),
              ],
            ),
            child: IconButton(
              icon: Icon(
                Icons.add,
                size: 30,
              ),
              onPressed: _increaseHeatLevel,
            ),
          ),
          SizedBox(height: 10),
          // Heat Level Indicator
          Container(
            width: 35,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(30),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.6),
                  offset: Offset(4, 4),
                  blurRadius: 10,
                ),
                BoxShadow(
                  color: Colors.white,
                  offset: Offset(-4, -4),
                  blurRadius: 10,
                ),
              ],
            ),
            child: Padding(
              padding: const EdgeInsets.all(5.0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.end,
                children: List.generate(3, (index) {
                  int levelIndex = 2 - index;
                  Color color;
                  if (levelIndex < _actualHeatLevel &&
                      levelIndex != _applyingLevel) {
                    color = Colors.orange;
                  } else if (_applyingLevel != null &&
                      levelIndex == _applyingLevel) {
                    color = _showBlink
                        ? Colors.orange.withOpacity(0.7)
                        : Colors.orange.withOpacity(0.2);
                  } else if (levelIndex < _selectedHeatLevel) {
                    color = Colors.white;
                  } else {
                    color = Colors.grey[300]!;
                  }
                  return Container(
                    height: 55.h,
                    margin: EdgeInsets.symmetric(vertical: 1),
                    decoration: BoxDecoration(
                      color: color,
                      borderRadius: BorderRadius.only(
                        topLeft:
                            levelIndex == 2 ? Radius.circular(30) : Radius.zero,
                        topRight:
                            levelIndex == 2 ? Radius.circular(30) : Radius.zero,
                        bottomLeft:
                            levelIndex == 0 ? Radius.circular(30) : Radius.zero,
                        bottomRight:
                            levelIndex == 0 ? Radius.circular(30) : Radius.zero,
                      ),
                    ),
                  );
                }),
              ),
            ),
          ),
          SizedBox(height: 10),
          // Decrease Button
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(30),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.6),
                  offset: Offset(4, 4),
                  blurRadius: 10,
                ),
              ],
            ),
            child: IconButton(
              icon: Icon(
                Icons.remove,
                size: 30,
              ),
              onPressed: _decreaseHeatLevel,
            ),
          ),
        ],
      ),
    );
  }
}
