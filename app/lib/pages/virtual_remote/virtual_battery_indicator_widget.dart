import 'package:flutter/material.dart';
import '../../custom_widgets/battery_indicator.dart';

class VirtualBatteryIndicatorWidget extends StatefulWidget {
  const VirtualBatteryIndicatorWidget({super.key});

  @override
  State<VirtualBatteryIndicatorWidget> createState() => _VirtualBatteryIndicatorWidgetState();
}

class _VirtualBatteryIndicatorWidgetState extends State<VirtualBatteryIndicatorWidget> {
  // Virtual battery level - starts at 85% and can be changed
  double _batteryLevel = 85.0;

  @override
  Widget build(BuildContext context) {
    return BatteryIndicator(
      trackAspectRatio: 2.0,
      trackHeight: 15,
      value: _batteryLevel / 100,
      icon: Text(
        '${_batteryLevel.toInt()}%',
        style: TextStyle(
          color: Colors.white,
          fontSize: 8,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }
}
