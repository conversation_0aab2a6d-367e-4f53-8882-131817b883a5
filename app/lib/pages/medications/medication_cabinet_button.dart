import 'package:flutter/material.dart';
import 'package:auto_route/auto_route.dart';
import '../../routing/app_pages.gr.dart';

@RoutePage()
class MedicationCabinetButton extends StatelessWidget {
  const MedicationCabinetButton({super.key});

  @override
  Widget build(BuildContext context) {
    return Positioned(
      bottom: 20.0,
      right: 15.0,
      width: 80,
      height: 80,
      child: FloatingActionButton(
        onPressed: () {
          context.router.push(MedicationCabinetRoute());
        },
        backgroundColor: Theme.of(context).primaryColor,
        shape: const CircleBorder(),
        child: const Icon(Icons.medical_services_rounded, color: Colors.white, size: 35,),
      ),
    );
  }
}

class FloatingMedicationCabinetButton extends StatelessWidget {
  final Widget child;
  const FloatingMedicationCabinetButton({super.key, required this.child});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        child,
        const MedicationCabinetButton(),
      ],
    );
  }
}
