import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:design_system/design/theme.dart';
import '../../routing/app_pages.gr.dart';
@RoutePage()
 class WelcomePage extends StatelessWidget {

  const WelcomePage({

    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return  PopScope(
      canPop: false,
      child: Scaffold(
      body: SingleChildScrollView(
        child: Container(
          width:1.sw ,
          height: 1.sh,
          color: Theme.of(context).primaryColor,
          child: Column(
          children: [
            SizedBox(height: .38.sh,),
            SvgPicture.asset('assets/logo/juno_logo_violet.svg', color: AppTheme.loginAppBarColor, width: 200,),
            SizedBox(height: .25.sh,),
            GestureDetector(
              onTap: (){
                context.router.push(const SignUpRoute());
              },
              child: Container(
                width: .6.sw,
                height: .13.sw,
                decoration: BoxDecoration(
                  border: Border.all(color: Color(0xffBEBADE),width: 1),
                  borderRadius : BorderRadius.only(
                    topLeft: Radius.circular(25),
                    topRight: Radius.circular(25),
                    bottomLeft: Radius.circular(25),
                    bottomRight: Radius.circular(25),
                  ),
                  color : AppTheme.loginAppBarColor,
                  boxShadow: const [
                    BoxShadow(
                      color: Color(0x40000000),
                      blurRadius: 4.0,
                      offset: Offset(0, 1),
                    ),
                  ],
                ),
                child: Center(child: Text("Sign Up",style: Theme.of(context).textTheme.bodySmall!.copyWith(fontSize: 20),)),
              ),
            ),
            SizedBox(
              height: 15,
            ),
            Container(
              alignment: Alignment.center,
              padding: EdgeInsets.symmetric(horizontal: 90),
              child: Row(
                children: [
                  Expanded(
                    child: Divider(
                      color: Colors.white38,
                      thickness: 1,
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8.0),
                    child: Text(
                      "or",
                      style: TextStyle(color: Colors.white),
                    ),
                  ),
                  Expanded(
                    child: Divider(
                      color: Colors.white38,
                      thickness: 1,
                    ),
                  ),
                ],
              ),
            ),
            GestureDetector(
              onTap: (){
                context.router.push(const LoginRoute());
              },
              child: Container(
                width: .7.sw,
                height: .12.sw,
                decoration: BoxDecoration(
                  borderRadius : BorderRadius.only(
                    topLeft: Radius.circular(25),
                    topRight: Radius.circular(25),
                    bottomLeft: Radius.circular(25),
                    bottomRight: Radius.circular(25),
                  ),
                  color : Theme.of(context).primaryColor,
                ),
                child: Container(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(child: Text("Already have an account? ",style: Theme.of(context).textTheme.bodyMedium!.copyWith(fontSize: 16),)),
                      Container(
                        child: Text(
                          "Log In",
                          style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                            fontSize: 16,
                            fontWeight: FontWeight.w800,
                            decoration: TextDecoration.underline,
                            decorationColor: Color(0xFFFFB854),
                            color: Color(0xFFFFB854),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),

        ],
          ),
        ),
      ),
      ),
    );
  }
}
