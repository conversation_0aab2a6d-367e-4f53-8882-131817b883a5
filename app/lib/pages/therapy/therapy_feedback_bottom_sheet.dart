import 'package:account_management/account_management.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:analytics/analytics.dart';
import 'package:get_it/get_it.dart';
import 'package:flutter_svg/svg.dart';
import '../../custom_widgets/emoji_slider.dart';

class TherapyFeedbackBottomSheet extends StatefulWidget {
  final String sessionId;

  const TherapyFeedbackBottomSheet({
    required this.sessionId,
    Key? key,
  }) : super(key: key);

  @override
  State<TherapyFeedbackBottomSheet> createState() =>
      _TherapyFeedbackBottomSheetState();

  static void show(BuildContext context, String sessionId) {
    try {
      showModalBottomSheet<void>(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (context) => MultiBlocProvider(
          providers: [
            BlocProvider(
              create: (context) => GetIt.instance<TherapyFeedbackBloc>()
                ..add(TherapyFeedbackEvent.loadSessionData(sessionId)),
            ),
            BlocProvider(
              create: (context) => GetIt.instance<ManagePeriodTrackingBloc>(),
            ),
          ],
          child: TherapyFeedbackBottomSheet(sessionId: sessionId),
        ),
      );
    } catch (e) {
      print('❌ Error showing therapy feedback bottom sheet: $e');
      // Fallback: try to show a simple dialog instead
      _showFallbackDialog(context, sessionId);
    }
  }

  static void _showFallbackDialog(BuildContext context, String sessionId) {
    try {
      showDialog<void>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Therapy Feedback'),
          content: const Text(
              'How was your therapy session? We would love to hear your feedback!'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Skip'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                // Try to show the bottom sheet again after a delay
                Future.delayed(const Duration(milliseconds: 500), () {
                  show(context, sessionId);
                });
              },
              child: const Text('Give Feedback'),
            ),
          ],
        ),
      );
    } catch (e) {
      print('❌ Error showing fallback dialog: $e');
    }
  }
}

class _TherapyFeedbackBottomSheetState
    extends State<TherapyFeedbackBottomSheet> {
  int _painLevelBefore = 5;
  int _painLevelAfter = 5;
  List<SymptomModel> _selectedSymptoms = [];

  List<String> emoji = [
    '\u{1F601}', // 😁
    '\u{1F642}', // 🙂
    '\u{1F610}', // 😐
    '\u{1F615}', // 😕
    '\u{1F641}', // 🙁
    '\u{1F61E}', // 😞
    '\u{1F613}', // 😓
    '\u{1F623}', // 😣
    '\u{1F616}', // 😖
    '\u{1F635}\u{200D}\u{1F4AB}', // 😵‍💫
    '\u{1F635}\u{200D}\u{1F4AB}', // 😵‍💫
  ];

  List<String> pain_description = [
    'No Pain',
    'Discomfort',
    'Very Mild',
    'Mild',
    'Moderate',
    'Significant',
    'High',
    'Very High',
    'Intense',
    'Worst Pain',
    'Worst Pain',
  ];
  List<SymptomModel> _symptoms = [
    SymptomModel(name: 'Headache'),
    SymptomModel(name: 'Fatigue'),
    SymptomModel(name: 'Bloating'),
    SymptomModel(name: 'Back Pain'),
    SymptomModel(name: 'Cramps'),
    SymptomModel(name: 'Breakouts'),
  ];
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(50.r),
      ),
      child: DraggableScrollableSheet(
        initialChildSize: 0.8,
        minChildSize: 0.5,
        maxChildSize: 0.95,
        expand: false,
        builder: (context, scrollController) {
          return BlocConsumer<TherapyFeedbackBloc, TherapyFeedbackState>(
            listener: (context, state) {
              state.when(
                initial: () {},
                loading: () {},
                sessionLoaded: (session) {},
                submitting: () {},
                submitted: () {
                  Navigator.of(context).pop();
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Thank you for your feedback!'),
                      backgroundColor: Colors.green,
                    ),
                  );
                },
                error: (message) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(message),
                      backgroundColor: Colors.red,
                    ),
                  );
                },
              );
            },
            builder: (context, state) {
              return SingleChildScrollView(
                controller: scrollController,
                padding: EdgeInsets.all(20.r),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    // Handle bar
                    Center(
                      child: Container(
                        width: 40.w,
                        height: 4.h,
                        decoration: BoxDecoration(
                          color: Colors.grey[300],
                          borderRadius: BorderRadius.circular(2.r),
                        ),
                      ),
                    ),
                    SizedBox(height: 20.h),

                    // Title
                    Text(
                      'How was your therapy session?',
                      style: TextStyle(
                        fontSize: 30.sp,
                        fontWeight: FontWeight.bold,
                        color: Colors.black,
                      ),
                    ),
                    SizedBox(height: 20.h),

                    // Save and Skip buttons at the top
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        // Skip button
                        SizedBox(
                          height: 60.h,
                           width: .42.sw,
                          child: TextButton(
                            onPressed: () => Navigator.of(context).pop(),
                            style: TextButton.styleFrom(
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(50.r),
                                side: BorderSide(color: Colors.grey[400]!),
                              ),
                            ),
                            child: Text(
                              'Skip',
                              style: TextStyle(
                                fontSize: 24.sp,
                                color: Colors.grey[600],
                              ),
                            ),
                          ),
                        ),

                        // Save button
                        SizedBox(
                          height: 60.h,
                          width: .42.sw,
                          child: ElevatedButton(
                            onPressed: state.maybeWhen(
                              submitting: () => null,
                              orElse: () => _submitFeedback,
                            ),
                            style: ElevatedButton.styleFrom(
                              backgroundColor:
                                  const Color.fromRGBO(58, 38, 101, 1.0),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(50.r),
                              ),
                            ),
                            child: state.when(
                              submitting: () => SizedBox(
                                width: 20.w,
                                height: 20.h,
                                child: const CircularProgressIndicator(
                                  color: Colors.white,
                                  strokeWidth: 2,
                                ),
                              ),
                              initial: () => Text('Save',
                                  style: TextStyle(
                                      fontSize: 24.sp, color: Colors.white)),
                              loading: () => Text('Save',
                                  style: TextStyle(
                                      fontSize: 24.sp, color: Colors.white)),
                              sessionLoaded: (session) => Text('Save',
                                  style: TextStyle(
                                      fontSize: 24.sp, color: Colors.white)),
                              submitted: () => Text('Save',
                                  style: TextStyle(
                                      fontSize: 24.sp, color: Colors.white)),
                              error: (message) => Text('Save',
                                  style: TextStyle(
                                      fontSize: 24.sp, color: Colors.white)),
                            ),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 5.h),

                    // Session details section
                    // state.when(
                    //   initial: () => const SizedBox(),
                    //   loading: () =>
                    //       const Center(child: CircularProgressIndicator()),
                    //   sessionLoaded: (session) => _buildSessionDetails(session),
                    //   submitting: () =>
                    //       const Center(child: CircularProgressIndicator()),
                    //   submitted: () => const SizedBox(),
                    //   error: (message) => const SizedBox(),
                    // ),

                    SizedBox(height: 20.h),

                    // Pain level before therapy
                    _buildPainLevelSection(
                      title: 'Pain Level Before Therapy',
                      value: _painLevelBefore,
                      onChanged: (value) =>
                          setState(() => _painLevelBefore = value),
                    ),
                    SizedBox(height: 20.h),

                    // Pain level after therapy
                    _buildPainLevelSection(
                      title: 'Pain Level After Therapy',
                      value: _painLevelAfter,
                      onChanged: (value) =>
                          setState(() => _painLevelAfter = value),
                    ),
                    SizedBox(height: 20.h),

                    // Symptoms section
                    _buildSymptomsSection(),
                    SizedBox(height: 30.h),

                    // Add bottom padding for keyboard
                    SizedBox(height: MediaQuery.of(context).viewInsets.bottom),
                  ],
                ),
              );
            },
          );
        },
      ),
    );
  }

  Widget _buildPainLevelSection({
    required String title,
    required int value,
    required void Function(int) onChanged,
  }) {
    return Container(
      margin: EdgeInsets.zero,
      height: 0.5.sw,
      width: 0.90.sw,
      decoration: BoxDecoration(
        boxShadow: [
          BoxShadow(
            color: Color(0x40000000),
            blurRadius: 4.0,
            offset: Offset(0, 1),
          ),
        ],
        borderRadius: BorderRadius.circular(32),
        color: Color.fromRGBO(250, 242, 223, 1),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 30.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(height: 10),
            Text(
              title,
              style: TextStyle(
                color: Color.fromRGBO(58, 38, 101, 1.0),
                fontWeight: FontWeight.w700,
                fontSize: 18,
              ),
            ),
            SizedBox(height: 10),
            Text(
              value.toString(),
              style: TextStyle(
                color: Color.fromRGBO(58, 38, 101, 1.0),
                fontWeight: FontWeight.w700,
                fontSize: 25,
              ),
            ),
            SizedBox(height: 10),
            Container(
              height: 75,
              margin: EdgeInsets.zero,
              child: Align(
                child: EmojiSlider(
                  key: Key(
                      'pain_slider_${title.toLowerCase().replaceAll(' ', '_')}'),
                  currentValue: value.toDouble(),
                  emojis: emoji,
                  minValue: 0,
                  maxValue: 10,
                  labels: pain_description,
                  onChanged: (handlerIndex, lowerValue, upperValue) {
                    onChanged((lowerValue as double).toInt());
                  },
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 5.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'No Pain',
                    style: TextStyle(color: Colors.black),
                  ),
                  Text(
                    'Worst Pain',
                    style: TextStyle(color: Colors.black),
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget _buildSymptomsSection() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 12.0),
      child: Container(
        decoration: BoxDecoration(
          boxShadow: [
            BoxShadow(
              color: Color(0x40000000),
              blurRadius: 4.0,
              offset: Offset(0, 1),
            ),
          ],
          borderRadius: BorderRadius.circular(32),
          color: Color.fromRGBO(250, 242, 223, 1),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(height: 10),
            Text(
              "Symptoms",
              style: TextStyle(
                color: Color.fromRGBO(58, 38, 101, 1.0),
                fontWeight: FontWeight.w700,
                fontSize: 22,
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(15.0),
              child: GridView.builder(
                padding: EdgeInsets.zero,
                shrinkWrap: true,
                physics: NeverScrollableScrollPhysics(),
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 3,
                  crossAxisSpacing: 10.0,
                  mainAxisSpacing: 0.0,
                  mainAxisExtent: 80.0,
                ),
                itemCount: _symptoms.length,
                itemBuilder: (gridContext, index) {
                  final isSelected = _selectedSymptoms
                      .any((symptom) => symptom.name == _symptoms[index].name);
                  return GestureDetector(
                    onTap: () {
                      setState(() {
                        if (isSelected) {
                          _selectedSymptoms.removeWhere((symptom) =>
                              symptom.name == _symptoms[index].name);
                        } else {
                          _selectedSymptoms.add(_symptoms[index]);
                        }
                      });
                    },
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          height: 38,
                          width: 67,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(32),
                            color: isSelected
                                ? Color.fromRGBO(247, 166, 0, 1)
                                : Colors.white,
                          ),
                          child: Padding(
                            padding: const EdgeInsets.all(4.0),
                            child: SvgPicture.asset(
                              'assets/home/<USER>' ', '_')}.svg',
                              colorFilter: ColorFilter.mode(
                                isSelected
                                    ? Colors.white
                                    : Color.fromRGBO(88, 66, 148, 1),
                                BlendMode.srcIn,
                              ),
                              fit: BoxFit.fitHeight,
                              height: 20,
                              width: 20,
                            ),
                          ),
                        ),
                        SizedBox(height: 4),
                        Text(
                          _symptoms[index].name,
                          style: TextStyle(
                            color: Color.fromRGBO(58, 38, 101, 1.0),
                            fontWeight: FontWeight.w700,
                            fontSize: 15,
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _submitFeedback() {
    // First, store the symptoms and pain level in the period tracking system (today's date)
    // This will store the symptoms and pain level before therapy as "overall pain level" in the symptom tracking system
    try {
      // Create a PeriodTrackingModel with today's date, symptoms, and pain level
      final periodTrackingData = PeriodTrackingModel(
        date: DateTime.now(),
        symptoms: _selectedSymptoms,
        painLevel: _painLevelBefore,
        flowLevel: null, // Not setting flow level for therapy feedback
        lastUpdated: null, // Will be set by the system
      );

      // Add the complete period tracking data (equivalent to clicking "Done" in symptom tracking)
      context.read<ManagePeriodTrackingBloc>().add(
            ManagePeriodTrackingEvent.addPeriodTracking(periodTrackingData),
          );

      print('✅ Stored symptoms and pain level in period tracking system');
    } catch (e) {
      print('❌ Error storing symptoms and pain level: $e');
      // Continue with feedback submission even if symptom storage fails
    }

    // Then submit the therapy feedback (pain levels before/after)
    context.read<TherapyFeedbackBloc>().add(
          TherapyFeedbackEvent.submitFeedback(
            sessionId: widget.sessionId,
            feedbackText:
                null, // No feedback text since we removed the text box
            painLevelBefore: _painLevelBefore,
            painLevelAfter: _painLevelAfter,
          ),
        );
  }
}
