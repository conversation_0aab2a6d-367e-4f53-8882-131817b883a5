import 'package:flutter/material.dart';
import 'package:design_system/design/theme.dart';
import 'package:google_fonts/google_fonts.dart';


class Help extends StatelessWidget {
  final String? why;
  final String? what;

  const Help({Key? key, this.why, this.what}) : super(key: key);

  void _showHelpDialog(BuildContext context) {
    showModalBottomSheet<void>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (_) => _HelpDialog(why: why, what: what),
    );
  }

  @override
  Widget build(BuildContext context) {
    return IconButton(
      icon: Icon(Icons.help_outline_rounded, size: 35, color: AppTheme.primaryColor),
      onPressed: () => _showHelpDialog(context),
    );
  }
}

class _HelpDialog extends StatelessWidget {
  final String? why;
  final String? what;

  const _HelpDialog({Key? key, this.why, this.what}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        GestureDetector(
          onTap: () => Navigator.of(context).pop(),
          child: Container(
            color: Colors.black.withOpacity(0.5),
          ),
        ),
        DraggableScrollableSheet(
          initialChildSize: 0.52,
          minChildSize: 0.2,
          maxChildSize: 0.6,
          builder: (context, scrollController) {
            return Container(
              alignment: Alignment.center,
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
              ),
              padding: const EdgeInsets.all(16),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  if (why != null) ...[
                     Text("Why we ask this?",
                       textAlign: TextAlign.center,
                       style: GoogleFonts.inter(
                      fontSize: 20,
                      fontWeight: FontWeight.w500,
                      color: AppTheme.primaryColor,
                    ),),
                    const SizedBox(height: 8),
                    Text(why! ,
                      textAlign: TextAlign.center,
                      style: GoogleFonts.inter(
                      fontSize: 16,
                      fontWeight: FontWeight.w400,
                      color: Color(0xFF26204A),
                    ),),
                    const SizedBox(height: 16),
                  ],
                  if (what != null) ...[
                    Text("What it means",
                      textAlign: TextAlign.center,
                      style: GoogleFonts.inter(
                      fontSize: 20,
                      fontWeight: FontWeight.w500,
                      color: AppTheme.primaryColor,
                    ),),
                    const SizedBox(height: 8),
                    Text(what! ,
                      textAlign: TextAlign.center,
                      style: GoogleFonts.inter(
                      fontSize: 16,
                      fontWeight: FontWeight.w400,
                      color: Color(0xFF26204A),
                    ),),
                    const SizedBox(height: 16),
                    Text("If you’re unsure, you can skip for now. \n You can always update this later." ,
                      textAlign: TextAlign.center,
                      style: GoogleFonts.inter(
                        fontSize: 16,
                        fontWeight: FontWeight.w400,
                        color: Color(0xFF26204A),
                      ),),
                  ],
                  SizedBox(
                    height: 20,
                  ),
                  Align(
                    alignment: Alignment.center,
                    child: SizedBox(
                      width: 250, // adjust as needed
                      child: ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppTheme.primaryColor,
                          padding: const EdgeInsets.symmetric(vertical: 14),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        onPressed: () => Navigator.of(context).pop(),
                        child: Text("Continue",
                            style: GoogleFonts.mulish(color: Colors.white, fontSize: 20)),
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ],
    );
  }
}



