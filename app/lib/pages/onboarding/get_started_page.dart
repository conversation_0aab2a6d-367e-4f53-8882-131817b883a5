import 'package:account_management/application/onboardin_form_bloc/onboarding_form_bloc.dart';
import 'package:account_management/di/di.dart';
import 'package:account_management/domain/model/health_data.dart';
import 'package:auto_route/annotations.dart';
import 'package:auto_route/auto_route.dart';
import 'package:date_picker_plus/date_picker_plus.dart';
import 'package:design_system/design/theme.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:skeletonizer/skeletonizer.dart';
import '../../custom_widgets/curved_app_bar.dart';
import '../../routing/app_pages.gr.dart';
import 'package:flutter_svg/svg.dart';
import 'help_info_button.dart';

@RoutePage()
class GetStartedPage extends StatefulWidget {
  @override
  _GetStartedPageState createState() => _GetStartedPageState();
}

class _GetStartedPageState extends State<GetStartedPage> {
  int cycleLength = 28;
  int periodDays = 4;
  List<String>? contraception = [];
  DateTime dob = DateTime.now().subtract(Duration(days: 365 * 10));
  DateTime lastPeriod = DateTime.now();
  final PageController _controller = PageController();
  int currentPage = 0;
  bool get isLastPage => currentPage == 4; // 5 cards/pages (0 to 4)

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _goNext() {
    if (isLastPage) {
      context.router.push(const WelcomeRoute());
    } else {
      _controller.nextPage(duration: Duration(milliseconds: 500), curve: Curves.easeIn);
    }
  }

  void _goBack() {
    _controller.previousPage(duration: Duration(milliseconds: 500), curve: Curves.easeIn);
  }


  void _onDone(){
      context.read<OnboardingFormBloc>().add(OnboardingFormEvent.updateOnboardingForm(
          HealthDataModel(
            cycleLength: cycleLength,
            periodLength: periodDays,
            contraceptionType: contraception,
            lastPeriodDate: lastPeriod,
          ),
          dob
      ));
      context.router.push(HomeRoute());
  }
  @override
  Widget build(BuildContext context) {
    return BlocListener<OnboardingFormBloc, OnboardingFormState>(
      listener: (context, state) {
        // TODO: implement listener
      },
      child: PopScope(
        canPop: false,
        child: Scaffold(
          extendBodyBehindAppBar: true,
          appBar: CurvedAppBar(
          appBarColor: AppTheme.primaryColor ,
          logoColor: AppTheme.loginAppBarColor,
          height: .35.sw, // The height of your curved app bar
        ),
          body: Stack(
            children: [
              PageView(
                controller: _controller,
                onPageChanged: (index) => setState(() => currentPage = index),
                children: [
                  _buildPage(card: _buildbirthDayCard(), title: "AGE", info:
                  Help(
                    why: "Knowing your age helps us personalize your cycle predictions.",
                  ),
                    circleImage:  SvgPicture.asset('assets/onboarding/cake.svg', width: 200,), ),
                  _buildPage(
                    card: _buildCycleLengthCard(),title: "CYCLE",
                    info:Help(
                      why: "Knowing your cycle length helps us: \n • Predict your next period \n• Tailor insights to your unique pattern",
                      what: "Your cycle length is the number of days from the first day of one period to the day before the next one starts.",
                    ),
                    circleImage:  SvgPicture.asset('assets/onboarding/calendar.svg',width: 170,),),
                  _buildPage(
                    card: _buildPeriodDaysCard(),title: "FLOW",
                    info:Help(
                      why: "Knowing this helps us track your flow more accurately, and tailor insights to your cycle. ",
                      what: "Count how many days you usually bleed - from the first day to the last.",
                    ),
                    circleImage:  SvgPicture.asset('assets/onboarding/drops.svg',width: 190,),),
                  _buildPage(
                      card: _buildlastPeriodDayCard(),
                      title: "LAST PERIOD",
                      info:Help(
                        why: "Knowing this helps us start tracking your cycle right away and give your more accurate predictions from the start.",
                        what: "Choose the first day of your most recent period.",
                      ),),
                    _buildPage(
                      card: _buildContraceptionCard(),
                      title: "BIRTH CONTROL",
                      info:Help(
                        why: "Knowing your contraception method helps us tailor predictions, since different methods can affect your cycle.",
                      ),
                    ),
                ],
              ),
              //
              isLastPage
                  ? SizedBox.shrink()
                  : Positioned(
                bottom: 38,
                left: 0,
                right: 0, // Add these to center it horizontally
                child: Center( // Ensures the child is centered
                  child: Container(
                    height: 48,
                    width: 140,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(30),
                      border: Border.all(
                        color: AppTheme.primaryColor,
                        width: 1.5,
                      ),
                    ),
                    child: TextButton(
                      onPressed: _goNext,
                      style: ButtonStyle(
                        overlayColor: MaterialStateProperty.all(
                          Colors.white.withOpacity(0.1),
                        ),
                        shape: MaterialStateProperty.all(
                          RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        minimumSize: MaterialStateProperty.all(
                          Size(0, 36),
                        ),
                        padding: MaterialStateProperty.all(
                          EdgeInsets.symmetric(horizontal: 10),
                        ),
                        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      ),
                      child: Text(
                        'Skip',
                        style: GoogleFonts.mulish(
                          fontSize: 20,
                          fontWeight: FontWeight.w600,
                          color: AppTheme.primaryColor,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              Positioned(
                top: 170,
                left: 0,
                right: 0,
                child: Center(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: List.generate(5, (index) {
                      final isPast = index < currentPage;
                      final isCurrent = index == currentPage;
                      return Container(
                        margin: EdgeInsets.symmetric(horizontal: 3),
                        width: isCurrent ? 50 : 41,
                        height: isCurrent ? 8 : 4,
                        decoration: BoxDecoration(
                          color: isCurrent
                              ? AppTheme.primaryColor
                              : isPast
                              ? Color(0xFF8375F4)  // passed
                              : Color(0xFFD9D9D9), // future
                          borderRadius: BorderRadius.circular(5)
                        ),
                      );
                    }),
                  )

                ),
              ),

              Positioned(
                bottom: 30,
                right: 20,
                child: GestureDetector(
                  onTap: () {
                    if (isLastPage) {
                      _onDone();
                    } else {
                      _goNext();
                    }
                  },
                  child: Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      color: AppTheme.primaryColor,
                      shape: BoxShape.circle,
                    ),
                    alignment: Alignment.center,
                    child: isLastPage
                        ? Text(
                      'Done',
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 18,
                        color: Colors.white,
                      ),
                    )
                        : const Icon(
                      Icons.keyboard_arrow_right_rounded,
                      size: 35,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),

              if (currentPage!=0)
                Positioned(
                  bottom: 30,
                  left: 20,
                  child: GestureDetector(
                    onTap: () {
                      _goBack();
                    },
                    child: Container(
                      width: 60,
                      height: 60,
                      decoration: BoxDecoration(
                        color: AppTheme.primaryColor,
                        borderRadius: BorderRadius.circular(30)
                      ),
                      alignment: Alignment.center,
                      child: Icon(
                        Icons.keyboard_arrow_left_rounded,
                        size: 35,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }


  Widget _buildPage({
    required Widget card,
    required String title,
    required Help info,
    Widget? circleImage,
  }) {
    return Center(
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 32),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(
              height: 0.11.sh,
            ),
            Container(
              padding: EdgeInsets.only(top: 10),
              decoration: BoxDecoration(
                shape: BoxShape.rectangle,
                borderRadius: BorderRadius.circular(20),
                color: Color(0xFFECE8F8),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black26,
                    blurRadius: 4,
                    offset: Offset(0, 4),
                  )
                ],
              ),
              child: Column(
                children: [
                  Row(
                    children: [
                      Container(
                        padding: EdgeInsets.only(left: 10, bottom: 2),
                        width: 50,
                        alignment: Alignment.center,
                        child: Text(
                          "Q${currentPage + 1}",
                          style: TextStyle(
                            fontSize: 24,
                            color: AppTheme.primaryColor,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      Expanded(
                        child: Container(
                          height: 40,
                          alignment: Alignment.center,
                          child: Text(
                            title,
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              color: AppTheme.primaryColor,
                              fontWeight: FontWeight.w800,
                              fontSize: 24,
                            ),
                          ),
                        ),
                      ),
                      Container(
                        padding: EdgeInsets.only(bottom: 3),
                        width: 50,
                        child: info,
                      )
                    ],
                  ),
                  if (circleImage != null)
                    Container(
                      height: 170,
                      margin: EdgeInsets.only(bottom: 10),
                      child: circleImage,
                    ),
                  Container(child: card),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCycleLengthCard() {
    return Card(
      margin: EdgeInsets.zero,
      color: Color(0xFFFAF2DF),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          children: [
            SizedBox(height: 5),
            Text(
              'How long is your average cycle?',
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Colors.black,
                fontSize: 20,
                fontWeight: FontWeight.w500,
              ),
            ),
            SizedBox(height: 15,),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: IconButton(
                    icon: Icon(Icons.remove, size: 32, color: AppTheme.primaryColor,),
                    style: ButtonStyle(
                      elevation: MaterialStateProperty.all(4),
                      shadowColor: MaterialStateProperty.all(Colors.black),
                      backgroundColor: MaterialStateProperty.all(Color(0xFFFFFDF7)),
                    ),
                    onPressed: () {
                      setState(() {
                        if (cycleLength >10) cycleLength--;
                      });
                    },
                  ),
                ),
                SizedBox(
                  width: 10,
                ),
                Text(
                  '$cycleLength',
                  style: GoogleFonts.openSans(
                    textStyle: TextStyle(
                      fontSize: 30,
                      fontWeight: FontWeight.w400,
                      color: Colors.black,
                    ),
                  ),
                ),
                SizedBox(
                  width: 10,
                ),
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: IconButton(
                    icon: Icon(
                        Icons.add,
                        size: 32,
                        color: AppTheme.primaryColor,

                    ),
                    style: ButtonStyle(
                      elevation: MaterialStateProperty.all(4),
                      shadowColor: MaterialStateProperty.all(Colors.black),
                      backgroundColor: MaterialStateProperty.all(Color(0xFFFFFDF7)),
                    ),
                    onPressed: () {
                      setState(() {
                        if (cycleLength < 50) cycleLength++;
                      });
                    },
                  ),
                ),
              ],
            ),
            SizedBox(height: 15,),
            Text(
              'The average cycle is between 23-35 days',
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Colors.black,
                fontSize: 15,
              ),
            ),
            SizedBox(height: 10,),
          ],
        ),
      ),
    );
  }

  Widget _buildPeriodDaysCard() {
    return Card(
     margin: EdgeInsets.zero,
     color:  Color(0xFFFAF2DF),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      child: Padding(
        padding: const EdgeInsets.all(10.0),
        child: Column(
          children: [
            SizedBox(height: 5),
            Text(
              'How many days is your period?',
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Colors.black,
                fontSize: 20,
                fontWeight: FontWeight.w500,
              ),
            ),
            SizedBox(height: 15,),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: IconButton(
                    icon: Icon(Icons.remove, size: 33, color: AppTheme.primaryColor,),
                    style: ButtonStyle(
                      elevation: MaterialStateProperty.all(4),
                      shadowColor: MaterialStateProperty.all(Colors.black),
                      backgroundColor: MaterialStateProperty.all(Color(0xFFFFFDF7)),
                    ),
                    onPressed: () {
                      setState(() {
                        if (periodDays > 0) periodDays--;
                      });
                    },
                  ),
                ),
                SizedBox(
                  width: 10,
                ),
                Text(
                  '$periodDays',
                  style: GoogleFonts.openSans(
                    textStyle: TextStyle(
                      fontSize: 30,
                      fontWeight: FontWeight.w400,
                      color: Colors.black,
                    ),
                  ),
                ),
                SizedBox(
                  width: 10,
                ),
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: IconButton(
                    style: ButtonStyle(
                      elevation: MaterialStateProperty.all(4),
                      shadowColor: MaterialStateProperty.all(Colors.black),
                      backgroundColor: MaterialStateProperty.all(Color(0xFFFFFDF7)),
                    ),
                    icon: Icon(Icons.add, size: 32, color: AppTheme.primaryColor,),
                    onPressed: () {
                      setState(() {
                        if (periodDays < 15) periodDays++;
                      });
                    },
                  ),
                ),
              ],
            ),
            SizedBox(height: 15,),
            Text(
              'The average period usually lasts 3-5 days',
              style: TextStyle(
                color: Colors.black,
                fontSize: 15,
              ),
            ),
            SizedBox(height: 10,),

          ],
        ),
      ),
    );
  }

  Widget _buildContraceptionCard() {
    return Card(
      margin: EdgeInsets.zero,
      color: Color(0xFFFAF2DF),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 0),
        child: Column(
          children: [
            Text(
              'What contraception(s) do you use?',
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Colors.black,
                fontSize: 19,
                fontWeight: FontWeight.w500,
              ),
            ),
            SizedBox(height: 8),
            Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    _buildImageOption('pill', 'assets/onboarding/capsules.svg'),
                    _buildImageOption('ring','assets/onboarding/ring.svg'),
                    _buildImageOption('hormonal IUD', 'assets/onboarding/hormonal-iud.svg'),
                  ],
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    _buildImageOption('implant', 'assets/onboarding/implant.svg'),
                    _buildImageOption('patch', 'assets/onboarding/patch.svg'),
                    _buildImageOption('depo-shot', 'assets/onboarding/shot.svg'),
                  ],),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    _buildImageOption('copper IUD', 'assets/onboarding/copper-iud.svg'),
                    _buildImageOption('other', 'assets/onboarding/other.svg'),
                    _buildImageOption('none', 'assets/onboarding/none.svg'),
                  ],),
              ],
            )

          ],
        ),
      ),
    );
  }

  Widget _buildImageOption(String value, String imagePath) {
    final bool isSelected = contraception!.contains(value);

    return Column(
      children: [
        GestureDetector(
          onTap: () {
            setState(() {
              if (isSelected) {
                contraception!.remove(value);
              } else {
                contraception!.add(value);
              }
            });
          },
          child: Container(
            margin: EdgeInsets.symmetric(vertical: 7, horizontal: 5),
            padding: EdgeInsets.symmetric(horizontal: 8),
            width: 75,
            height: 75,
            decoration: BoxDecoration(
              border: Border.all(
                color: isSelected ? AppTheme.primaryColor : Colors.grey.shade300,
                width: 2,
              ),
              borderRadius: BorderRadius.circular(12),
              color: isSelected ? AppTheme.primaryColor.withOpacity(0.1) : Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black12,
                  blurRadius: 4,
                  offset: Offset(0, 2),
                )
              ],
            ),
            alignment: Alignment.center,
            child: SvgPicture.asset(
              imagePath,
              width: 50,
              height: 50,
            ),
          ),
        ),
        Container(
          width: 105,
          height: 20,
          child: Text(
            textAlign: TextAlign.center,
            value,
            style: Theme.of(context).textTheme.bodyLarge!.copyWith(
              fontWeight: FontWeight.w400,
              color: isSelected ? AppTheme.primaryColor : Colors.black,
            ),
          ),
        ),
      ],
    );
  }


  Widget _buildbirthDayCard() {
    return Card(
      margin: EdgeInsets.zero,
      color: Color(0xFFFAF2DF),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 14),
        child: Column(
          children: [
            SizedBox(height: 5),
            Text(
              'When is your birthday?',
              style: TextStyle(
                color: Colors.black,
                fontSize: 20,
                fontWeight: FontWeight.w500,
              ),
            ),
            SizedBox(height: 5),
            SizedBox(
              width: 0.9.sw,
              height: 135,
              child: CupertinoTheme(
                data: CupertinoThemeData(
                  textTheme: CupertinoTextThemeData(
                    dateTimePickerTextStyle: TextStyle(
                      color: Color(0xff30285D),
                      fontSize: 18,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ),
                child: CupertinoDatePicker(
                  mode: CupertinoDatePickerMode.date,
                  initialDateTime: dob ?? DateTime.now(),
                  minimumDate: DateTime(1960, 10, 10),
                  maximumDate: DateTime.now().subtract(Duration(days: 365 * 10)),
                  onDateTimeChanged: (DateTime newDate) {
                    setState(() {
                      dob = newDate;
                    });
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
  Widget _buildlastPeriodDayCard() {
    return Card(
      margin: EdgeInsets.zero,
      color: Color(0xFFFAF2DF),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          children: [
            Text(
                'When was your last period?',
              style: TextStyle(
                color: Colors.black,
                fontSize: 20,
                fontWeight: FontWeight.w500,
              ),
            ),
            SizedBox(height: 20),
            SizedBox(
                width: .8.sw,
                height: 300,
                child: DatePicker(
                  initialDate: lastPeriod ?? DateTime.now(),
                  minDate: DateTime.now().subtract(Duration(days: 670)),
                  maxDate: DateTime.now(),
                  padding: EdgeInsets.zero,
                  currentDate: DateTime.now(),
                  selectedDate: lastPeriod,
                  onDateSelected: (date) {
                    lastPeriod = date;
                  },
                  currentDateDecoration: const BoxDecoration(),
                  currentDateTextStyle:
                  GoogleFonts.mulish(
                      color: const Color(0xff30285D),
                      fontWeight: FontWeight.w400,
                      fontSize: 18,
                  ),
                  daysOfTheWeekTextStyle: const TextStyle(
                    color: AppTheme.primaryColor,
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                  disabledCellsDecoration: const BoxDecoration(),
                  disabledCellsTextStyle: const TextStyle(color: Colors.grey),
                  enabledCellsDecoration: const BoxDecoration(),
                  enabledCellsTextStyle:
                  GoogleFonts.mulish(
                      color: const Color(0xff71456F),
                      fontWeight: FontWeight.w400,
                      fontSize: 18,),
                  initialPickerType: PickerType.days,
                  selectedCellDecoration: BoxDecoration(
                    color: Theme
                        .of(context)
                        .primaryColor,
                    shape: BoxShape.circle,
                  ),
                  selectedCellTextStyle: GoogleFonts.mulish(
                    color: const Color(0xffFBF0D5),
                    fontWeight: FontWeight.w400,
                    fontSize: 20,
                  ),
                  leadingDateTextStyle:
                  GoogleFonts.mulish(
                      color: AppTheme.primaryColor,
                      fontWeight: FontWeight.bold,
                      fontSize: 22,
                  ),
                  slidersColor: const Color(0xff30285D),
                  highlightColor: const Color(0xff30285D),
                  slidersSize: 20,
                  splashColor: Theme
                      .of(context)
                      .primaryColor
                      .withOpacity(0.5),
                  splashRadius: 0,
                  centerLeadingDate: true,
                ),
              ),
            SizedBox(height: 10),
          ],
        ),
      ),
    );
  }

}
