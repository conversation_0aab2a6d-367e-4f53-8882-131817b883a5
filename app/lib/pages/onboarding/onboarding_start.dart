import 'package:account_management/application/onboardin_form_bloc/onboarding_form_bloc.dart';
import 'package:account_management/domain/model/health_data.dart';
import 'package:flutter/material.dart';
import 'package:auto_route/auto_route.dart';
import '../../routing/app_pages.gr.dart';
import 'package:design_system/design/theme.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

@RoutePage()
class OnboardingStartScreen extends StatelessWidget {
  const OnboardingStartScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    int cycleLength = 28;
    int periodDays = 4;
    List<String>? contraception = [];
    DateTime dob = DateTime.now().subtract(Duration(days: 365 * 10));
    DateTime lastPeriod = DateTime.now();

    return BlocListener<OnboardingFormBloc, OnboardingFormState>(
      listener: (context, state) {
        // TODO: implement listener}
      },
      child: Scaffold(
        body: Container(
          width: double.infinity,
          height: double.infinity,
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              colors: [Color(0xFF554C9F), Color(0xFFE8CDFE)],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Spacer(flex: 3),
                Text(
                  "Let’s Get Started",
                  style: GoogleFonts.dmSerifDisplay(
                    fontSize: 36,
                    fontWeight: FontWeight.w400,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                // Subtitle
                Text(
                  "Help us tailor things to you!\nAnswer a few quick questions\nso we can make smarter\npredictions.",
                  style: GoogleFonts.mulish(
                    fontSize: 16,
                    color: Colors.white,
                    height: 1.2,
                  ),
                  textAlign: TextAlign.center,
                ),
                const Spacer(flex: 2),
                GestureDetector(
                  onTap: (){
                    context.read<OnboardingFormBloc>().add(OnboardingFormEvent.updateOnboardingForm(
                        HealthDataModel(
                          cycleLength: cycleLength,
                          periodLength: periodDays,
                          contraceptionType: contraception,
                          lastPeriodDate: lastPeriod,
                        ),
                        dob
                    ));
                    context.router.push(GetStartedRoute());
                  },
                  child: Container(
                    width: screenWidth * 0.6,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    decoration: BoxDecoration(
                      color: const Color(0xFF5E3EAA),
                      borderRadius: BorderRadius.circular(30),
                    ),
                    child: Center(
                      child: Text(
                        "Continue",
                        style: GoogleFonts.mulish(
                          fontSize: 20,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
                ),

                const SizedBox(height: 12),
                // Skip Button
                GestureDetector(
                  onTap: () async{
                    context.read<OnboardingFormBloc>().add(OnboardingFormEvent.updateOnboardingForm(
                        HealthDataModel(
                          cycleLength: cycleLength,
                          periodLength: periodDays,
                          contraceptionType: contraception,
                          lastPeriodDate: lastPeriod,
                        ),
                        dob
                    ));
                    context.router.push(HomeRoute());
                  },
                  child: Container(
                    width: screenWidth * 0.6,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    decoration: BoxDecoration(
                      color: Colors.transparent,
                      border: Border.all(color: AppTheme.primaryColor.withOpacity(0.6), width: 1.5),
                      borderRadius: BorderRadius.circular(30),
                    ),
                    child: Center(
                      child: Text(
                        "Skip",
                        style: GoogleFonts.mulish(
                          fontSize: 20,
                          color: AppTheme.primaryColor,
                        ),
                      ),
                    ),
                  ),
                ),

                const Spacer(),
              ],
            ),
          ),
      ),
    ),
);
  }
}
