import 'package:flutter/material.dart';
import 'package:auto_route/auto_route.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import '../../routing/app_pages.gr.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';

@RoutePage()
class ProductShowcasePage extends StatefulWidget {
  const ProductShowcasePage({Key? key}) : super(key: key);

  @override
  State<ProductShowcasePage> createState() => _ProductShowcasePageState();
}

class _ProductShowcasePageState extends State<ProductShowcasePage> {
  final PageController _controller = PageController();
  int currentPage = 0;
  bool get isLastPage => currentPage == 2; // Adjust based on number of pages

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _goNext() {
    if(isLastPage){
      context.router.push(const WelcomeRoute());
    }
    else {
      _controller.nextPage(
          duration: Duration(milliseconds: 500), curve: Curves.easeIn);
    }
  }

  void _skip() {
    context.router.push(const WelcomeRoute());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFFFBF0D5), // top color
              Color(0xFFF8EEFF), // bottom color
            ],
          ),
        ),
        child: Stack(
          children: [
            PageView(
              controller: _controller,
              onPageChanged: (index) => setState(() => currentPage = index),
              children: [
                _buildPage(
                  color: Colors.transparent,
                  title: 'Pain Relief Made Smarter',
                  description: 'Manage your pain, monitor your cycle and log your symptoms. Use Juno+ with the Lilly device, or on its own.',
                  image: Image.asset(
                    'assets/product_showcase/product.png',
                    scale: 0.85,
                  )
                ),
                _buildPage(
                  color: Colors.transparent,
                  title: 'Targeted Relief, Controlled by You',
                  description: 'Experience targeted pain relief with Lilly - seamlessly controlled via the Juno+ app.',
                    image: Container(
                      child: Image.asset(
                        'assets/product_showcase/juno_phone.png',
                      ),
                    )
                ),
                _buildPage(
                  color: Colors.transparent,
                  title: 'Built for Everyday',
                  description: 'Log periods, symptoms, moods and medications. Built by people who get it.',
                    image: Container(
                      padding: EdgeInsets.only(left: 0.05.sw),
                      child: Image.asset(
                        'assets/product_showcase/circles.png',
                      ),
                    )
                ),
              ],
            ),

            Positioned(
              bottom: 25,
              left: 10,
              child: TextButton(
                onPressed: _skip,
                child: Text( isLastPage?
                  '': 'Skip',
                  style: GoogleFonts.mulish(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF584294),
                  ),
                ),
              ),
            ),

            // Page indicator & Next/Done button bottom
            Positioned(
              bottom: 40,
              left: .40.sw,
                child: SmoothPageIndicator(
                  controller: _controller,
                  count: 3,
                  effect: ExpandingDotsEffect(
                    activeDotColor: Theme.of(context).primaryColor,
                    expansionFactor: 2.5,
                    dotColor: Color(0xFFD7D2F9),
                    dotHeight: 15,
                    dotWidth: 15,
                    spacing: 6.0
                  ),
                ),),
            Positioned(
              bottom: 7,
              right: 10,
              child: TextButton(
                onPressed: _goNext,
                style: TextButton.styleFrom(
                  padding: EdgeInsets.all(24), // Controls size / "circular" feel
                  backgroundColor: Colors.transparent,
                  foregroundColor: Color(0xFF584294), // Icon/Text color
                  shape: null, // No shape enforced
                ),
                child: isLastPage
                    ? Container(
                  padding: EdgeInsets.only(bottom: 4),
                      child: Text(
                        'Done',
                        style: TextStyle(
                          fontWeight: FontWeight.w400,
                          fontSize: 18,
                        ),
                      ),
                    )
                    : Icon(
                  Icons.keyboard_arrow_right_rounded,
                  size: 40,
                ),
              ),
            )

          ],
        ),
      ),
    );
  }

  Widget _buildPage({
    required Color color,
    required String title,
    required String description,
    required Widget image,
    
  }) {
    return Container(
      color: color,
      padding: EdgeInsets.symmetric(vertical: 80),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            alignment: Alignment.center,
            child: SizedBox(
              height: 135,
              width: 320,
              child: Text(
                title,
                textAlign: TextAlign.center,
                style: GoogleFonts.dmSerifDisplay(
                  height: 1.0,
                  fontSize: 45,
                  color: Color(0xFF26204A),
                  ),
              ),
            ),
          ),
          Stack(
            children: [
              Container(
                width: 1.0.sw,
                height: 1.0.sw,
                alignment: Alignment.center,
                child: Image.asset('assets/product_showcase/product_showcase_dot.png',
              ),
            ),
              Container(
                width: 1.0.sw,
                height: 1.0.sw,
                alignment: Alignment.center,
                child: image,
              )
            ]
          ),
          SizedBox(
            height: 10,
          ),
          Container(
            alignment: Alignment.center,
            child: SizedBox(
              width: 275,
              child: Text(
                description,
                textAlign: TextAlign.center,
                style: GoogleFonts.averiaSerifLibre(
                  height: 1.0,
                  fontSize: 18,
                  color: Color(0xFF26204A),
               ),
              ),
            ),
          ),
          Spacer(),
        ],
      ),
    );
  }
}
