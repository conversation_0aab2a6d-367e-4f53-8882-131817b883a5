// dart format width=80
// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// InjectableConfigGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:analytics/domain/facade/analytics_facade.dart' as _i414;
import 'package:flutter/material.dart' as _i409;
import 'package:get_it/get_it.dart' as _i174;
import 'package:injectable/injectable.dart' as _i526;
import 'package:juno_plus/di.dart' as _i770;
import 'package:juno_plus/di_modules.dart' as _i292;
import 'package:juno_plus/services/therapy_feedback_service.dart' as _i418;
import 'package:notifications/domain/facade/scheduled_notifications_facade.dart'
    as _i828;

extension GetItInjectableX on _i174.GetIt {
// initializes the registration of main-scope dependencies inside of GetIt
  _i174.GetIt init({
    String? environment,
    _i526.EnvironmentFilter? environmentFilter,
  }) {
    final gh = _i526.GetItHelper(
      this,
      environment,
      environmentFilter,
    );
    final appModule = _$AppModule();
    final externalDependencyModule = _$ExternalDependencyModule();
    gh.singleton<_i409.GlobalKey<_i409.NavigatorState>>(
        () => appModule.navigatorKey);
    gh.factory<_i828.ScheduledNotificationsFacade>(
      () => externalDependencyModule.notificationsFacade,
      instanceName: 'externalNotificationsFacade',
    );
    gh.lazySingleton<_i418.TherapyFeedbackService>(
        () => _i418.TherapyFeedbackService(
              gh<_i828.ScheduledNotificationsFacade>(),
              gh<_i414.IAnalyticsFacade>(),
              gh<_i409.GlobalKey<_i409.NavigatorState>>(),
            ));
    return this;
  }
}

class _$AppModule extends _i292.AppModule {}

class _$ExternalDependencyModule extends _i770.ExternalDependencyModule {}
