import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:json_annotation/json_annotation.dart';

part 'therapy_chart_data_model.g.dart';

@JsonSerializable(explicitToJson: true)
class TherapyChartDataModel {
  final String sessionId;
  final DateTime sessionStartTime;
  final DateTime? sessionEndTime;
  final double tensLevel;
  final double heatLevel;
  final double? painLevelBefore;
  final double? painLevelAfter;
  final int sessionDurationMinutes;
  final String sessionLabel; // 'Session 1', 'Session 2', etc.

  TherapyChartDataModel({
    required this.sessionId,
    required this.sessionStartTime,
    this.sessionEndTime,
    required this.tensLevel,
    required this.heatLevel,
    this.painLevelBefore,
    this.painLevelAfter,
    required this.sessionDurationMinutes,
    required this.sessionLabel,
  });

  factory TherapyChartDataModel.fromJson(Map<String, dynamic> json) =>
      _$TherapyChartDataModelFromJson(json);

  Map<String, dynamic> toJson() => _$TherapyChartDataModelToJson(this);

  // Create empty session data
  factory TherapyChartDataModel.empty(String sessionId, DateTime sessionTime) {
    return TherapyChartDataModel(
      sessionId: sessionId,
      sessionStartTime: sessionTime,
      sessionEndTime: null,
      tensLevel: 0,
      heatLevel: 0,
      painLevelBefore: null,
      painLevelAfter: null,
      sessionDurationMinutes: 0,
      sessionLabel: 'No Data',
    );
  }

  // Copy with method
  TherapyChartDataModel copyWith({
    String? sessionId,
    DateTime? sessionStartTime,
    DateTime? sessionEndTime,
    double? tensLevel,
    double? heatLevel,
    double? painLevelBefore,
    double? painLevelAfter,
    int? sessionDurationMinutes,
    String? sessionLabel,
  }) {
    return TherapyChartDataModel(
      sessionId: sessionId ?? this.sessionId,
      sessionStartTime: sessionStartTime ?? this.sessionStartTime,
      sessionEndTime: sessionEndTime ?? this.sessionEndTime,
      tensLevel: tensLevel ?? this.tensLevel,
      heatLevel: heatLevel ?? this.heatLevel,
      painLevelBefore: painLevelBefore ?? this.painLevelBefore,
      painLevelAfter: painLevelAfter ?? this.painLevelAfter,
      sessionDurationMinutes:
          sessionDurationMinutes ?? this.sessionDurationMinutes,
      sessionLabel: sessionLabel ?? this.sessionLabel,
    );
  }
}
