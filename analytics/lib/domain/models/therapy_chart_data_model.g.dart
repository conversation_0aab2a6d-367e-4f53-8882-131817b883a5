// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'therapy_chart_data_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TherapyChartDataModel _$TherapyChartDataModelFromJson(
        Map<String, dynamic> json) =>
    TherapyChartDataModel(
      sessionId: json['sessionId'] as String,
      sessionStartTime: DateTime.parse(json['sessionStartTime'] as String),
      sessionEndTime: json['sessionEndTime'] == null
          ? null
          : DateTime.parse(json['sessionEndTime'] as String),
      tensLevel: (json['tensLevel'] as num).toDouble(),
      heatLevel: (json['heatLevel'] as num).toDouble(),
      painLevelBefore: (json['painLevelBefore'] as num?)?.toDouble(),
      painLevelAfter: (json['painLevelAfter'] as num?)?.toDouble(),
      sessionDurationMinutes: (json['sessionDurationMinutes'] as num).toInt(),
      sessionLabel: json['sessionLabel'] as String,
    );

Map<String, dynamic> _$TherapyChartDataModelToJson(
        TherapyChartDataModel instance) =>
    <String, dynamic>{
      'sessionId': instance.sessionId,
      'sessionStartTime': instance.sessionStartTime.toIso8601String(),
      'sessionEndTime': instance.sessionEndTime?.toIso8601String(),
      'tensLevel': instance.tensLevel,
      'heatLevel': instance.heatLevel,
      'painLevelBefore': instance.painLevelBefore,
      'painLevelAfter': instance.painLevelAfter,
      'sessionDurationMinutes': instance.sessionDurationMinutes,
      'sessionLabel': instance.sessionLabel,
    };
