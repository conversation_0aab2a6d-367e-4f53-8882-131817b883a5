// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'therapy_feedback_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TherapyFeedbackModel _$TherapyFeedbackModelFromJson(
        Map<String, dynamic> json) =>
    TherapyFeedbackModel(
      feedbackText: json['feedbackText'] as String?,
      painLevelBefore: (json['painLevelBefore'] as num?)?.toInt(),
      painLevelAfter: (json['painLevelAfter'] as num?)?.toInt(),
      feedbackRequested: json['feedbackRequested'] as bool? ?? false,
      feedbackCompleted: json['feedbackCompleted'] as bool? ?? false,
      feedbackSubmittedAt:
          firestoreTimestampFromJson(json['feedbackSubmittedAt']),
    );

Map<String, dynamic> _$TherapyFeedbackModelToJson(
        TherapyFeedbackModel instance) =>
    <String, dynamic>{
      'feedbackText': instance.feedbackText,
      'painLevelBefore': instance.painLevelBefore,
      'painLevelAfter': instance.painLevelAfter,
      'feedbackRequested': instance.feedbackRequested,
      'feedbackCompleted': instance.feedbackCompleted,
      'feedbackSubmittedAt':
          firestoreTimestampToJson(instance.feedbackSubmittedAt),
    };
