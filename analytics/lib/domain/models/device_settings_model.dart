import 'package:analytics/domain/models/timestamp_converters.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
part 'device_settings_model.g.dart';

@JsonSerializable(explicitToJson: true)
class DeviceSettingsModel {
  int heatLevel;
  int tensLevel;
  int tensMode;
  int batteryLevel;
  @JsonKey(
      fromJson: firestoreTimestampFromJson, toJson: firestoreTimestampToJson)
  DateTime? timestamp;

  DeviceSettingsModel({
    required this.heatLevel,
    required this.tensLevel,
    required this.tensMode,
    required this.batteryLevel,
    required this.timestamp,
  });

  factory DeviceSettingsModel.fromJson(Map<String, dynamic> json) =>
      _$DeviceSettingsModelFromJson(json);

  Map<String, dynamic> toJson() => _$DeviceSettingsModelToJson(this);

  // Local JSON serialization (ISO8601 for timestamp)
  factory DeviceSettingsModel.fromLocalJson(Map<String, dynamic> json) {
    return DeviceSettingsModel(
      heatLevel: (json['heatLevel'] as num?)?.toInt() ?? 0,
      tensLevel: (json['tensLevel'] as num?)?.toInt() ?? 0,
      tensMode: (json['tensMode'] as num?)?.toInt() ?? 0,
      batteryLevel: (json['batteryLevel'] as num?)?.toInt() ?? 0,
      timestamp: json['timestamp'] != null
          ? DateTime.tryParse(json['timestamp'] as String)
          : null,
    );
  }

  Map<String, dynamic> toLocalJson() => {
        'heatLevel': heatLevel,
        'tensLevel': tensLevel,
        'tensMode': tensMode,
        'batteryLevel': batteryLevel,
        'timestamp': timestamp?.toIso8601String(),
      };
}
