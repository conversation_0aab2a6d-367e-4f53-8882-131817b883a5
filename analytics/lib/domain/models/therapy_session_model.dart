import 'package:analytics/domain/models/device_connection_state_model.dart';
import 'package:analytics/domain/models/therapy_session_event_model.dart';
import 'package:analytics/domain/models/therapy_session_info_model.dart';
import 'package:analytics/domain/models/timestamp_converters.dart';
import 'package:analytics/domain/models/therapy_feedback_model.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:remote/domain/model/device_info_model.dart';
import 'device_settings_model.dart';
import 'device_snapshot_model.dart';

part 'therapy_session_model.g.dart';

// === ✅ Main Model ===
@JsonSerializable(explicitToJson: true)
class TherapySessionModel {
  final DeviceSettingsModel initialSettings;
  final DeviceSettingsModel finalSettings;
  final DeviceSettingsModel mostUsedSettings;
  final List<TherapySessionEventModel> sessionEvents;
  final List<DeviceSnapshotModel> deviceSnapshot;
  final List<DeviceConnectionStateModel> deviceConnectionStates;
  final List<Map<String, dynamic>> deviceLogs;
  final DeviceInfoModel deviceInformation;
  final TherapySessionInfoModel sessionInfo;
  final bool isSyncedToCloud;
  final String status;
  final TherapyFeedbackModel? feedback;

  @JsonKey(
      fromJson: firestoreTimestampFromJson, toJson: firestoreTimestampToJson)
  final DateTime? lastSnapshotTime;

  @JsonKey(
      fromJson: firestoreTimestampFromJson, toJson: firestoreTimestampToJson)
  final DateTime? lastSyncAttempt;

  TherapySessionModel({
    required this.initialSettings,
    required this.finalSettings,
    required this.mostUsedSettings,
    required this.sessionEvents,
    required this.deviceSnapshot,
    required this.deviceLogs,
    required this.deviceInformation,
    required this.sessionInfo,
    required this.deviceConnectionStates,
    this.isSyncedToCloud = false,
    this.status = 'active',
    this.feedback,
    this.lastSnapshotTime,
    this.lastSyncAttempt,
  });

  // === 🔁 Firestore serialization ===
  factory TherapySessionModel.fromJson(Map<String, dynamic> json) =>
      _$TherapySessionModelFromJson(json);

  Map<String, dynamic> toJson() => _$TherapySessionModelToJson(this);

  // === 🔁 Local JSON serialization ===
  factory TherapySessionModel.fromLocalJson(Map<String, dynamic> json) {
    return TherapySessionModel(
      initialSettings: DeviceSettingsModel.fromLocalJson(
          json['initialSettings'] as Map<String, dynamic>),
      finalSettings: DeviceSettingsModel.fromLocalJson(
          json['finalSettings'] as Map<String, dynamic>),
      mostUsedSettings: DeviceSettingsModel.fromLocalJson(
          json['mostUsedSettings'] as Map<String, dynamic>),
      sessionEvents: (json['sessionEvents'] as List<dynamic>? ?? [])
          .map((e) =>
              TherapySessionEventModel.fromLocalJson(e as Map<String, dynamic>))
          .toList(),
      deviceSnapshot: (json['deviceSnapshot'] as List<dynamic>? ?? [])
          .map((e) =>
              DeviceSnapshotModel.fromLocalJson(e as Map<String, dynamic>))
          .toList(),
      deviceLogs: (json['deviceLogs'] as List<dynamic>? ?? [])
          .map((e) => e as Map<String, dynamic>)
          .toList(),
      deviceInformation: DeviceInfoModel.fromJson(
          json['deviceInformation'] as Map<String, dynamic>),
      sessionInfo: TherapySessionInfoModel.fromLocalJson(
          json['sessionInfo'] as Map<String, dynamic>),
      isSyncedToCloud: json['isSyncedToCloud'] as bool? ?? false,
      status: json['status'] as String? ?? 'active',
      lastSnapshotTime: json['lastSnapshotTime'] != null
          ? DateTime.tryParse(json['lastSnapshotTime'] as String)
          : null,
      lastSyncAttempt: json['lastSyncAttempt'] != null
          ? DateTime.tryParse(json['lastSyncAttempt'] as String)
          : null,
      deviceConnectionStates: (json['deviceConnectionStates']
                  as List<dynamic>? ??
              [])
          .map((e) =>
              DeviceConnectionStateModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      feedback: json['feedback'] != null
          ? TherapyFeedbackModel.fromLocalJson(
              json['feedback'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toLocalJson() {
    return {
      'initialSettings': initialSettings.toLocalJson(),
      'finalSettings': finalSettings.toLocalJson(),
      'mostUsedSettings': mostUsedSettings.toLocalJson(),
      'sessionEvents': sessionEvents.map((e) => e.toLocalJson()).toList(),
      'deviceSnapshot': deviceSnapshot.map((e) => e.toLocalJson()).toList(),
      'deviceLogs': deviceLogs,
      'deviceInformation': deviceInformation.toJson(),
      'sessionInfo': sessionInfo.toLocalJson(),
      'isSyncedToCloud': isSyncedToCloud,
      'status': status,
      'lastSnapshotTime': localTimestampToJson(lastSnapshotTime),
      'lastSyncAttempt': localTimestampToJson(lastSyncAttempt),
      'deviceConnectionStates':
          deviceConnectionStates.map((e) => e.toLocalJson()).toList(),
      'feedback': feedback?.toLocalJson(),
    };
  }

  // === 🧬 copyWith ===
  TherapySessionModel copyWith({
    DeviceSettingsModel? initialSettings,
    DeviceSettingsModel? finalSettings,
    DeviceSettingsModel? mostUsedSettings,
    List<TherapySessionEventModel>? sessionEvents,
    List<DeviceSnapshotModel>? deviceSnapshot,
    List<Map<String, dynamic>>? deviceLogs,
    DeviceInfoModel? deviceInformation,
    TherapySessionInfoModel? sessionInfo,
    bool? isSyncedToCloud,
    String? status,
    TherapyFeedbackModel? feedback,
    DateTime? lastSnapshotTime,
    DateTime? lastSyncAttempt,
    List<DeviceConnectionStateModel>? deviceConnectionStates,
  }) {
    return TherapySessionModel(
      initialSettings: initialSettings ?? this.initialSettings,
      finalSettings: finalSettings ?? this.finalSettings,
      mostUsedSettings: mostUsedSettings ?? this.mostUsedSettings,
      sessionEvents: sessionEvents ?? this.sessionEvents,
      deviceSnapshot: deviceSnapshot ?? this.deviceSnapshot,
      deviceLogs: deviceLogs ?? this.deviceLogs,
      deviceInformation: deviceInformation ?? this.deviceInformation,
      sessionInfo: sessionInfo ?? this.sessionInfo,
      isSyncedToCloud: isSyncedToCloud ?? this.isSyncedToCloud,
      status: status ?? this.status,
      feedback: feedback ?? this.feedback,
      lastSnapshotTime: lastSnapshotTime ?? this.lastSnapshotTime,
      lastSyncAttempt: lastSyncAttempt ?? this.lastSyncAttempt,
      deviceConnectionStates:
          deviceConnectionStates ?? this.deviceConnectionStates,
    );
  }

  // === 🏁 Create New Session ===
  factory TherapySessionModel.startNewSession({
    required DeviceSettingsModel initialSettings,
    required DeviceInfoModel deviceInformation,
    required TherapySessionInfoModel sessionInfo,
  }) {
    return TherapySessionModel(
      initialSettings: initialSettings,
      finalSettings: initialSettings,
      mostUsedSettings: initialSettings,
      sessionEvents: [],
      deviceSnapshot: [],
      deviceLogs: [],
      deviceInformation: deviceInformation,
      sessionInfo: sessionInfo,
      isSyncedToCloud: false,
      status: 'active',
      feedback: null,
      lastSnapshotTime: null,
      lastSyncAttempt: null,
      deviceConnectionStates: [],
    );
  }
}
