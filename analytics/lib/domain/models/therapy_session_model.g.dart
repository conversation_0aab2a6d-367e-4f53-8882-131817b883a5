// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'therapy_session_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TherapySessionModel _$TherapySessionModelFromJson(Map<String, dynamic> json) =>
    TherapySessionModel(
      initialSettings: DeviceSettingsModel.fromJson(
          json['initialSettings'] as Map<String, dynamic>),
      finalSettings: DeviceSettingsModel.fromJson(
          json['finalSettings'] as Map<String, dynamic>),
      mostUsedSettings: DeviceSettingsModel.fromJson(
          json['mostUsedSettings'] as Map<String, dynamic>),
      sessionEvents: (json['sessionEvents'] as List<dynamic>)
          .map((e) =>
              TherapySessionEventModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      deviceSnapshot: (json['deviceSnapshot'] as List<dynamic>)
          .map((e) => DeviceSnapshotModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      deviceLogs: (json['deviceLogs'] as List<dynamic>)
          .map((e) => e as Map<String, dynamic>)
          .toList(),
      deviceInformation: DeviceInfoModel.fromJson(
          json['deviceInformation'] as Map<String, dynamic>),
      sessionInfo: TherapySessionInfoModel.fromJson(
          json['sessionInfo'] as Map<String, dynamic>),
      deviceConnectionStates: (json['deviceConnectionStates'] as List<dynamic>)
          .map((e) =>
              DeviceConnectionStateModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      isSyncedToCloud: json['isSyncedToCloud'] as bool? ?? false,
      status: json['status'] as String? ?? 'active',
      feedback: json['feedback'] == null
          ? null
          : TherapyFeedbackModel.fromJson(
              json['feedback'] as Map<String, dynamic>),
      lastSnapshotTime: firestoreTimestampFromJson(json['lastSnapshotTime']),
      lastSyncAttempt: firestoreTimestampFromJson(json['lastSyncAttempt']),
    );

Map<String, dynamic> _$TherapySessionModelToJson(
        TherapySessionModel instance) =>
    <String, dynamic>{
      'initialSettings': instance.initialSettings.toJson(),
      'finalSettings': instance.finalSettings.toJson(),
      'mostUsedSettings': instance.mostUsedSettings.toJson(),
      'sessionEvents': instance.sessionEvents.map((e) => e.toJson()).toList(),
      'deviceSnapshot': instance.deviceSnapshot.map((e) => e.toJson()).toList(),
      'deviceConnectionStates':
          instance.deviceConnectionStates.map((e) => e.toJson()).toList(),
      'deviceLogs': instance.deviceLogs,
      'deviceInformation': instance.deviceInformation.toJson(),
      'sessionInfo': instance.sessionInfo.toJson(),
      'isSyncedToCloud': instance.isSyncedToCloud,
      'status': instance.status,
      'feedback': instance.feedback?.toJson(),
      'lastSnapshotTime': firestoreTimestampToJson(instance.lastSnapshotTime),
      'lastSyncAttempt': firestoreTimestampToJson(instance.lastSyncAttempt),
    };
