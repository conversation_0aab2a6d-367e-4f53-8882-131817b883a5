import 'dart:convert';
import 'package:analytics/domain/models/device_connection_state_model.dart';
import 'package:analytics/domain/models/device_snapshot_model.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:injectable/injectable.dart';
import 'package:flutter/foundation.dart';
import '../../domain/models/therapy_session_event_model.dart';
import '../../domain/models/therapy_session_model.dart';

abstract class ILocalTelemetryDataSource {
  Future<bool> saveEvent(TherapySessionEventModel event);
  Future<List<TherapySessionEventModel>> getEvents();
  Future<bool> markEventSynced(String eventId);
  Future<bool> saveTherapySession(TherapySessionModel session);
  Future<List<TherapySessionModel>> getTherapySessions();
  Future<bool> markSessionSynced(String sessionId);
  // Enhanced therapy session methods
  Future<TherapySessionModel?> getCurrentSession();
  Future<List<TherapySessionModel>> getUnsyncedSessions();
  Future<bool> updateTherapySession(TherapySessionModel session);
  Future<bool> deleteTherapySession(String sessionId);
  Future<bool> clearOldSessions({int keepCount = 5});
  // Clear all data methods (for debugging)
  Future<bool> clearAllTherapySessions();
  Future<bool> clearAllEvents();
  Future<bool> saveDeviceSnapshot(DeviceSnapshotModel snapshot);
  Future<bool> saveDeviceConnectionState(
      DeviceConnectionStateModel connectionState);
  Future<List<DeviceSnapshotModel>> getDeviceSnapshots();
  Future<List<DeviceConnectionStateModel>> getDeviceConnectionStates();
  //clear all device snapshots and connection states
  Future<bool> clearAllDeviceSnapshots();
  Future<bool> clearAllDeviceConnectionStates();
}

@LazySingleton(as: ILocalTelemetryDataSource)
class SharedPrefsTelemetryDataSource implements ILocalTelemetryDataSource {
  static const String _eventsKey = 'telemetry_events';
  static const String _therapySessionsKey = 'therapy_sessions';
  // Add these constants at the top of your class
  static const String _deviceSnapshotsKey = 'device_snapshots';
  static const String _deviceConnectionStatesKey = 'device_connection_states';

  final SharedPreferences _prefs;

  SharedPrefsTelemetryDataSource(this._prefs);

  @override
  Future<List<TherapySessionEventModel>> getEvents() async {
    final String? eventsJson = _prefs.getString(_eventsKey);
    if (eventsJson == null) return [];

    final List<dynamic> eventsList = jsonDecode(eventsJson);
    return eventsList
        .map((e) =>
            TherapySessionEventModel.fromLocalJson(e as Map<String, dynamic>))
        .toList();
  }

  @override
  Future<List<TherapySessionModel>> getTherapySessions() async {
    final String? sessionsJson = _prefs.getString(_therapySessionsKey);
    if (sessionsJson == null) return [];
    final List<dynamic> sessionsList = jsonDecode(sessionsJson);
    return sessionsList
        .map(
            (e) => TherapySessionModel.fromLocalJson(e as Map<String, dynamic>))
        .toList();
  }

  @override
  Future<bool> saveEvent(TherapySessionEventModel event) async {
    final events = await getEvents();
    events.add(event);

    final String eventsJson = jsonEncode(
      events.map((e) => e.toLocalJson()).toList(),
    );
    return await _prefs.setString(_eventsKey, eventsJson);
  }

  @override
  Future<bool> saveTherapySession(TherapySessionModel session) async {
    try {
      final sessions = await getTherapySessions();
      // Remove session with same id if exists
      final filtered = sessions
          .where(
              (s) => s.sessionInfo.sessionId != session.sessionInfo.sessionId)
          .toList();
      filtered.insert(0, session); // add new session at the start
      // Keep only latest 5
      final latest = filtered.take(5).toList();
      final String sessionsJson = jsonEncode(
        latest.map((e) => e.toLocalJson()).toList(),
      );
      return await _prefs.setString(_therapySessionsKey, sessionsJson);
    } catch (e, stack) {
      debugPrint('Error in saveTherapySession: $e\n$stack');
      return false;
    }
  }

  @override
  Future<bool> markEventSynced(String eventId) async {
    final events = await getEvents();
    final updatedEvents = events.map((event) {
      if (event.eventId == eventId) {
        return event.copyWith(isSyncedToCloud: true);
      }
      return event;
    }).toList();

    final String eventsJson = jsonEncode(
      updatedEvents.map((e) => e.toLocalJson()).toList(),
    );
    return await _prefs.setString(_eventsKey, eventsJson);
  }

  @override
  Future<bool> markSessionSynced(String sessionId) async {
    final sessions = await getTherapySessions();
    final updatedSessions = sessions.map((session) {
      if (session.sessionInfo.sessionId == sessionId) {
        return session.copyWith(isSyncedToCloud: true);
      }
      return session;
    }).toList();
    final String sessionsJson = jsonEncode(
      updatedSessions.map((e) => e.toLocalJson()).toList(),
    );
    return await _prefs.setString(_therapySessionsKey, sessionsJson);
  }

  // Enhanced therapy session methods implementation
  @override
  Future<TherapySessionModel?> getCurrentSession() async {
    try {
      final sessions = await getTherapySessions();
      return sessions
          .where((session) => session.status == 'active')
          .firstOrNull;
    } catch (e) {
      return null;
    }
  }

  @override
  Future<List<TherapySessionModel>> getUnsyncedSessions() async {
    try {
      final sessions = await getTherapySessions();
      return sessions.where((session) => !session.isSyncedToCloud).toList();
    } catch (e) {
      return [];
    }
  }

  @override
  Future<bool> updateTherapySession(TherapySessionModel session) async {
    try {
      final sessions = await getTherapySessions();
      final sessionIndex = sessions.indexWhere(
          (s) => s.sessionInfo.sessionId == session.sessionInfo.sessionId);

      if (sessionIndex == -1) {
        // Session not found, add it
        sessions.add(session);
      } else {
        // Update existing session
        sessions[sessionIndex] = session;
      }

      final sessionsJson = jsonEncode(
        sessions.map((session) => session.toLocalJson()).toList(),
      );
      return await _prefs.setString(_therapySessionsKey, sessionsJson);
    } catch (e) {
      return false;
    }
  }

  @override
  Future<bool> deleteTherapySession(String sessionId) async {
    try {
      final sessions = await getTherapySessions();
      sessions
          .removeWhere((session) => session.sessionInfo.sessionId == sessionId);

      final sessionsJson = jsonEncode(
        sessions.map((session) => session.toLocalJson()).toList(),
      );
      return await _prefs.setString(_therapySessionsKey, sessionsJson);
    } catch (e) {
      return false;
    }
  }

  Future<List<TherapySessionModel>> getLatestSessions({int limit = 5}) async {
    try {
      final sessions = await getTherapySessions();

      // Sort by start time (most recent first)
      sessions.sort((a, b) {
        final aTime = a.sessionInfo.therapyStartTime;
        final bTime = b.sessionInfo.therapyStartTime;
        if (bTime == null && aTime == null) return 0;
        if (bTime == null) return -1;
        if (aTime == null) return 1;
        return bTime.compareTo(aTime);
      });

      // Return only the latest sessions up to the limit
      return sessions.take(limit).toList();
    } catch (e) {
      return [];
    }
  }

  @override
  Future<bool> clearOldSessions({int keepCount = 5}) async {
    try {
      final sessions = await getTherapySessions();

      if (sessions.length <= keepCount) {
        return true; // Nothing to clear
      }

      // Sort by start time (most recent first)

      sessions.sort((a, b) {
        final aTime = a.sessionInfo.therapyStartTime;
        final bTime = b.sessionInfo.therapyStartTime;
        if (bTime == null && aTime == null) return 0;
        if (bTime == null) return -1;
        if (aTime == null) return 1;
        return bTime.compareTo(aTime);
      });

      // Keep only the latest sessions
      final sessionsToKeep = sessions.take(keepCount).toList();

      final sessionsJson = jsonEncode(
        sessionsToKeep.map((session) => session.toLocalJson()).toList(),
      );
      return await _prefs.setString(_therapySessionsKey, sessionsJson);
    } catch (e) {
      return false;
    }
  }

  // Clear all data methods (for debugging)
  @override
  Future<bool> clearAllTherapySessions() async {
    try {
      return await _prefs.remove(_therapySessionsKey);
    } catch (e) {
      return false;
    }
  }

  @override
  Future<bool> clearAllEvents() async {
    try {
      return await _prefs.remove(_eventsKey);
    } catch (e) {
      return false;
    }
  }

  @override
  Future<bool> saveDeviceSnapshot(DeviceSnapshotModel snapshot) async {
    try {
      final List<DeviceSnapshotModel> snapshots = await getDeviceSnapshots();
      snapshots.add(snapshot);
      final String jsonStr = jsonEncode(
        snapshots.map((e) => e.toLocalJson()).toList(),
      );
      return await _prefs.setString(_deviceSnapshotsKey, jsonStr);
    } catch (e) {
      return false;
    }
  }

  @override
  Future<List<DeviceSnapshotModel>> getDeviceSnapshots() async {
    try {
      final String? jsonStr = _prefs.getString(_deviceSnapshotsKey);
      if (jsonStr == null) return [];
      final List<dynamic> list = jsonDecode(jsonStr);
      return list
          .map((e) =>
              DeviceSnapshotModel.fromLocalJson(e as Map<String, dynamic>))
          .toList();
    } catch (e) {
      return [];
    }
  }

  @override
  Future<bool> clearAllDeviceSnapshots() async {
    try {
      return await _prefs.remove(_deviceSnapshotsKey);
    } catch (e) {
      return false;
    }
  }

  @override
  Future<bool> saveDeviceConnectionState(
      DeviceConnectionStateModel connectionState) async {
    try {
      final List<DeviceConnectionStateModel> states =
          await getDeviceConnectionStates();
      states.add(connectionState);
      final String jsonStr = jsonEncode(
        states.map((e) => e.toLocalJson()).toList(),
      );
      return await _prefs.setString(_deviceConnectionStatesKey, jsonStr);
    } catch (e) {
      return false;
    }
  }

  @override
  Future<List<DeviceConnectionStateModel>> getDeviceConnectionStates() async {
    try {
      final String? jsonStr = _prefs.getString(_deviceConnectionStatesKey);
      if (jsonStr == null) return [];
      final List<dynamic> list = jsonDecode(jsonStr);
      return list
          .map((e) => DeviceConnectionStateModel.fromLocalJson(
              e as Map<String, dynamic>))
          .toList();
    } catch (e) {
      return [];
    }
  }

  @override
  Future<bool> clearAllDeviceConnectionStates() async {
    try {
      return await _prefs.remove(_deviceConnectionStatesKey);
    } catch (e) {
      return false;
    }
  }
}
