// Application layer exports
export 'application/analytics_initialization_bloc/analytics_initialization_bloc.dart';
export 'application/therapy_feedback_bloc/therapy_feedback_bloc.dart';
export 'application/therapy_analytics_bloc/therapy_analytics_bloc.dart';
export 'application/therapy_analytics_bloc/therapy_analytics_event.dart';
export 'application/therapy_analytics_bloc/therapy_analytics_state.dart';

// Domain layer exports
export 'domain/facade/analytics_facade.dart';
export 'domain/models/therapy_session_model.dart';
export 'domain/models/therapy_chart_data_model.dart';

// Infrastructure layer exports
export 'infrastructure/analytics_facade_impl.dart';
export 'infrastructure/datasources/cloud_telemetry_datasource.dart';
export 'infrastructure/datasources/local_telemetry_datasource.dart';

// Services exports
export 'infrastructure/services/device_snapshot_service.dart';
export 'infrastructure/services/session_state_manager.dart';
export 'infrastructure/services/analytics_initialization_service.dart';

// Dependency injection
export 'di/di.dart';

// Initialize function to bootstrap the analytics package
import 'di/di.dart';

/// Initialize the analytics package
/// This should be called before using any analytics functionality
Future<void> initializeAnalytics() async {
  // Configure dependency injection
  configureDependencies(env: 'prod');
}
