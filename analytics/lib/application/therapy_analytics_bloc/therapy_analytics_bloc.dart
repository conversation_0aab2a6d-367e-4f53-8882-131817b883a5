import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:injectable/injectable.dart';
import 'package:fpdart/fpdart.dart';
import '../../domain/facade/analytics_facade.dart';
import '../../domain/models/therapy_session_model.dart';
import '../../domain/models/therapy_chart_data_model.dart';
import 'therapy_analytics_event.dart';
import 'therapy_analytics_state.dart';

@injectable
class TherapyAnalyticsBloc
    extends Bloc<TherapyAnalyticsEvent, TherapyAnalyticsState> {
  final IAnalyticsFacade _analyticsFacade;

  TherapyAnalyticsBloc(this._analyticsFacade)
      : super(const TherapyAnalyticsState.initial()) {
    on<TherapyAnalyticsEvent>((event, emit) async {
      await event.when(
        loadWeeklyData: () => _onLoadWeeklyData(emit),
        loadMonthlyData: () => _onLoadMonthlyData(emit),
        loadCustomRangeData: (startDate, endDate) =>
            _onLoadCustomRangeData(emit, startDate, endDate),
        refreshData: () => _onRefreshData(emit),
      );
    });
  }

  Future<void> _onLoadWeeklyData(Emitter<TherapyAnalyticsState> emit) async {
    emit(const TherapyAnalyticsState.loading());
    await _loadLastFiveSessions(emit, 'weekly');
  }

  Future<void> _onLoadMonthlyData(Emitter<TherapyAnalyticsState> emit) async {
    emit(const TherapyAnalyticsState.loading());
    await _loadLastFiveSessions(emit, 'monthly');
  }

  Future<void> _onLoadCustomRangeData(Emitter<TherapyAnalyticsState> emit,
      DateTime startDate, DateTime endDate) async {
    emit(const TherapyAnalyticsState.loading());
    await _loadLastFiveSessions(emit, 'custom');
  }

  Future<void> _onRefreshData(Emitter<TherapyAnalyticsState> emit) async {
    final currentState = state;
    await currentState.when(
      initial: () => _onLoadWeeklyData(emit),
      loading: () => _onLoadWeeklyData(emit),
      loaded: (chartData, startDate, endDate, viewType) async {
        emit(const TherapyAnalyticsState.loading());
        await _loadLastFiveSessions(emit, viewType);
      },
      error: (message) => _onLoadWeeklyData(emit),
    );
  }

  Future<void> _loadLastFiveSessions(
    Emitter<TherapyAnalyticsState> emit,
    String viewType,
  ) async {
    try {
      final sessionsResult =
          await _analyticsFacade.getTherapySessionsWithFallback();

      sessionsResult.mapBoth(
        onLeft: (error) => emit(TherapyAnalyticsState.error(message: error)),
        onRight: (sessions) {
          final chartData = _processLastFiveSessionsToChartData(sessions);
          final now = DateTime.now();
          emit(TherapyAnalyticsState.loaded(
            chartData: chartData,
            startDate:
                now.subtract(const Duration(days: 30)), // Arbitrary start date
            endDate: now,
            viewType: viewType,
          ));
        },
      );
    } catch (e) {
      emit(TherapyAnalyticsState.error(
          message: 'Failed to load therapy data: $e'));
    }
  }

  List<TherapyChartDataModel> _processLastFiveSessionsToChartData(
    List<TherapySessionModel> sessions,
  ) {
    // Filter sessions that have valid start times and sort by most recent
    final validSessions = sessions
        .where((session) => session.sessionInfo.therapyStartTime != null)
        .toList();

    // Sort by start time (most recent first)
    validSessions.sort((a, b) => b.sessionInfo.therapyStartTime!
        .compareTo(a.sessionInfo.therapyStartTime!));

    // Take only the last 5 sessions
    final lastFiveSessions = validSessions.take(5).toList();

    // Convert to chart data models
    final List<TherapyChartDataModel> chartData = [];

    for (int i = 0; i < lastFiveSessions.length; i++) {
      final session = lastFiveSessions[i];
      chartData.add(_convertSessionToChartData(session, i + 1));
    }

    // If we have fewer than 5 sessions, fill with empty data
    for (int i = lastFiveSessions.length; i < 5; i++) {
      chartData.add(TherapyChartDataModel.empty(
        'empty_${i + 1}',
        DateTime.now().subtract(Duration(days: i + 1)),
      ));
    }

    return chartData.reversed.toList(); // Reverse to show oldest first
  }

  TherapyChartDataModel _calculateDayData(
    DateTime date,
    List<TherapySessionModel> sessions,
    List<String> dayLabels,
  ) {
    double totalTens = 0;
    double totalHeat = 0;
    double totalPainBefore = 0;
    double totalPainAfter = 0;
    int painBeforeCount = 0;
    int painAfterCount = 0;
    int totalMinutes = 0;

    for (final session in sessions) {
      // Use most used settings for the session
      totalTens += session.mostUsedSettings.tensLevel;
      totalHeat += session.mostUsedSettings.heatLevel;

      // Calculate session duration
      if (session.sessionInfo.therapyStartTime != null &&
          session.sessionInfo.therapyEndTime != null) {
        final duration = session.sessionInfo.therapyEndTime!
            .difference(session.sessionInfo.therapyStartTime!);
        totalMinutes += duration.inMinutes;
      }

      // Extract pain levels from feedback
      if (session.feedback != null) {
        if (session.feedback!.painLevelBefore != null) {
          totalPainBefore += session.feedback!.painLevelBefore!;
          painBeforeCount++;
        }
        if (session.feedback!.painLevelAfter != null) {
          totalPainAfter += session.feedback!.painLevelAfter!;
          painAfterCount++;
        }
      }
    }

    // This method is no longer used - replaced by _convertSessionToChartData
    throw UnimplementedError('Use _convertSessionToChartData instead');
  }

  TherapyChartDataModel _convertSessionToChartData(
    TherapySessionModel session,
    int sessionNumber,
  ) {
    // Calculate session duration
    int durationMinutes = 0;
    if (session.sessionInfo.therapyStartTime != null &&
        session.sessionInfo.therapyEndTime != null) {
      final duration = session.sessionInfo.therapyEndTime!
          .difference(session.sessionInfo.therapyStartTime!);
      durationMinutes = duration.inMinutes;
    }

    return TherapyChartDataModel(
      sessionId: session.sessionInfo.sessionId,
      sessionStartTime: session.sessionInfo.therapyStartTime!,
      sessionEndTime: session.sessionInfo.therapyEndTime,
      tensLevel: session.mostUsedSettings.tensLevel.toDouble(),
      heatLevel: session.mostUsedSettings.heatLevel.toDouble(),
      painLevelBefore: session.feedback?.painLevelBefore?.toDouble(),
      painLevelAfter: session.feedback?.painLevelAfter?.toDouble(),
      sessionDurationMinutes: durationMinutes,
      sessionLabel: 'Session $sessionNumber',
    );
  }
}
