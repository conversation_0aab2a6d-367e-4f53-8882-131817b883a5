import 'package:freezed_annotation/freezed_annotation.dart';
import '../../domain/models/therapy_chart_data_model.dart';

part 'therapy_analytics_state.freezed.dart';

@freezed
class TherapyAnalyticsState with _$TherapyAnalyticsState {
  const factory TherapyAnalyticsState.initial() = _Initial;
  const factory TherapyAnalyticsState.loading() = _Loading;
  const factory TherapyAnalyticsState.loaded({
    required List<TherapyChartDataModel> chartData,
    required DateTime startDate,
    required DateTime endDate,
    required String viewType, // 'weekly', 'monthly', 'custom'
  }) = _Loaded;
  const factory TherapyAnalyticsState.error({
    required String message,
  }) = _Error;
}
