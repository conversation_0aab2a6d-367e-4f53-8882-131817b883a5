// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'therapy_analytics_event.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$TherapyAnalyticsEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadWeeklyData,
    required TResult Function() loadMonthlyData,
    required TResult Function(DateTime startDate, DateTime endDate)
        loadCustomRangeData,
    required TResult Function() refreshData,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadWeeklyData,
    TResult? Function()? loadMonthlyData,
    TResult? Function(DateTime startDate, DateTime endDate)?
        loadCustomRangeData,
    TResult? Function()? refreshData,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadWeeklyData,
    TResult Function()? loadMonthlyData,
    TResult Function(DateTime startDate, DateTime endDate)? loadCustomRangeData,
    TResult Function()? refreshData,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadWeeklyData value) loadWeeklyData,
    required TResult Function(_LoadMonthlyData value) loadMonthlyData,
    required TResult Function(_LoadCustomRangeData value) loadCustomRangeData,
    required TResult Function(_RefreshData value) refreshData,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadWeeklyData value)? loadWeeklyData,
    TResult? Function(_LoadMonthlyData value)? loadMonthlyData,
    TResult? Function(_LoadCustomRangeData value)? loadCustomRangeData,
    TResult? Function(_RefreshData value)? refreshData,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadWeeklyData value)? loadWeeklyData,
    TResult Function(_LoadMonthlyData value)? loadMonthlyData,
    TResult Function(_LoadCustomRangeData value)? loadCustomRangeData,
    TResult Function(_RefreshData value)? refreshData,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TherapyAnalyticsEventCopyWith<$Res> {
  factory $TherapyAnalyticsEventCopyWith(TherapyAnalyticsEvent value,
          $Res Function(TherapyAnalyticsEvent) then) =
      _$TherapyAnalyticsEventCopyWithImpl<$Res, TherapyAnalyticsEvent>;
}

/// @nodoc
class _$TherapyAnalyticsEventCopyWithImpl<$Res,
        $Val extends TherapyAnalyticsEvent>
    implements $TherapyAnalyticsEventCopyWith<$Res> {
  _$TherapyAnalyticsEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TherapyAnalyticsEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$LoadWeeklyDataImplCopyWith<$Res> {
  factory _$$LoadWeeklyDataImplCopyWith(_$LoadWeeklyDataImpl value,
          $Res Function(_$LoadWeeklyDataImpl) then) =
      __$$LoadWeeklyDataImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadWeeklyDataImplCopyWithImpl<$Res>
    extends _$TherapyAnalyticsEventCopyWithImpl<$Res, _$LoadWeeklyDataImpl>
    implements _$$LoadWeeklyDataImplCopyWith<$Res> {
  __$$LoadWeeklyDataImplCopyWithImpl(
      _$LoadWeeklyDataImpl _value, $Res Function(_$LoadWeeklyDataImpl) _then)
      : super(_value, _then);

  /// Create a copy of TherapyAnalyticsEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadWeeklyDataImpl implements _LoadWeeklyData {
  const _$LoadWeeklyDataImpl();

  @override
  String toString() {
    return 'TherapyAnalyticsEvent.loadWeeklyData()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoadWeeklyDataImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadWeeklyData,
    required TResult Function() loadMonthlyData,
    required TResult Function(DateTime startDate, DateTime endDate)
        loadCustomRangeData,
    required TResult Function() refreshData,
  }) {
    return loadWeeklyData();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadWeeklyData,
    TResult? Function()? loadMonthlyData,
    TResult? Function(DateTime startDate, DateTime endDate)?
        loadCustomRangeData,
    TResult? Function()? refreshData,
  }) {
    return loadWeeklyData?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadWeeklyData,
    TResult Function()? loadMonthlyData,
    TResult Function(DateTime startDate, DateTime endDate)? loadCustomRangeData,
    TResult Function()? refreshData,
    required TResult orElse(),
  }) {
    if (loadWeeklyData != null) {
      return loadWeeklyData();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadWeeklyData value) loadWeeklyData,
    required TResult Function(_LoadMonthlyData value) loadMonthlyData,
    required TResult Function(_LoadCustomRangeData value) loadCustomRangeData,
    required TResult Function(_RefreshData value) refreshData,
  }) {
    return loadWeeklyData(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadWeeklyData value)? loadWeeklyData,
    TResult? Function(_LoadMonthlyData value)? loadMonthlyData,
    TResult? Function(_LoadCustomRangeData value)? loadCustomRangeData,
    TResult? Function(_RefreshData value)? refreshData,
  }) {
    return loadWeeklyData?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadWeeklyData value)? loadWeeklyData,
    TResult Function(_LoadMonthlyData value)? loadMonthlyData,
    TResult Function(_LoadCustomRangeData value)? loadCustomRangeData,
    TResult Function(_RefreshData value)? refreshData,
    required TResult orElse(),
  }) {
    if (loadWeeklyData != null) {
      return loadWeeklyData(this);
    }
    return orElse();
  }
}

abstract class _LoadWeeklyData implements TherapyAnalyticsEvent {
  const factory _LoadWeeklyData() = _$LoadWeeklyDataImpl;
}

/// @nodoc
abstract class _$$LoadMonthlyDataImplCopyWith<$Res> {
  factory _$$LoadMonthlyDataImplCopyWith(_$LoadMonthlyDataImpl value,
          $Res Function(_$LoadMonthlyDataImpl) then) =
      __$$LoadMonthlyDataImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadMonthlyDataImplCopyWithImpl<$Res>
    extends _$TherapyAnalyticsEventCopyWithImpl<$Res, _$LoadMonthlyDataImpl>
    implements _$$LoadMonthlyDataImplCopyWith<$Res> {
  __$$LoadMonthlyDataImplCopyWithImpl(
      _$LoadMonthlyDataImpl _value, $Res Function(_$LoadMonthlyDataImpl) _then)
      : super(_value, _then);

  /// Create a copy of TherapyAnalyticsEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadMonthlyDataImpl implements _LoadMonthlyData {
  const _$LoadMonthlyDataImpl();

  @override
  String toString() {
    return 'TherapyAnalyticsEvent.loadMonthlyData()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoadMonthlyDataImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadWeeklyData,
    required TResult Function() loadMonthlyData,
    required TResult Function(DateTime startDate, DateTime endDate)
        loadCustomRangeData,
    required TResult Function() refreshData,
  }) {
    return loadMonthlyData();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadWeeklyData,
    TResult? Function()? loadMonthlyData,
    TResult? Function(DateTime startDate, DateTime endDate)?
        loadCustomRangeData,
    TResult? Function()? refreshData,
  }) {
    return loadMonthlyData?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadWeeklyData,
    TResult Function()? loadMonthlyData,
    TResult Function(DateTime startDate, DateTime endDate)? loadCustomRangeData,
    TResult Function()? refreshData,
    required TResult orElse(),
  }) {
    if (loadMonthlyData != null) {
      return loadMonthlyData();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadWeeklyData value) loadWeeklyData,
    required TResult Function(_LoadMonthlyData value) loadMonthlyData,
    required TResult Function(_LoadCustomRangeData value) loadCustomRangeData,
    required TResult Function(_RefreshData value) refreshData,
  }) {
    return loadMonthlyData(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadWeeklyData value)? loadWeeklyData,
    TResult? Function(_LoadMonthlyData value)? loadMonthlyData,
    TResult? Function(_LoadCustomRangeData value)? loadCustomRangeData,
    TResult? Function(_RefreshData value)? refreshData,
  }) {
    return loadMonthlyData?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadWeeklyData value)? loadWeeklyData,
    TResult Function(_LoadMonthlyData value)? loadMonthlyData,
    TResult Function(_LoadCustomRangeData value)? loadCustomRangeData,
    TResult Function(_RefreshData value)? refreshData,
    required TResult orElse(),
  }) {
    if (loadMonthlyData != null) {
      return loadMonthlyData(this);
    }
    return orElse();
  }
}

abstract class _LoadMonthlyData implements TherapyAnalyticsEvent {
  const factory _LoadMonthlyData() = _$LoadMonthlyDataImpl;
}

/// @nodoc
abstract class _$$LoadCustomRangeDataImplCopyWith<$Res> {
  factory _$$LoadCustomRangeDataImplCopyWith(_$LoadCustomRangeDataImpl value,
          $Res Function(_$LoadCustomRangeDataImpl) then) =
      __$$LoadCustomRangeDataImplCopyWithImpl<$Res>;
  @useResult
  $Res call({DateTime startDate, DateTime endDate});
}

/// @nodoc
class __$$LoadCustomRangeDataImplCopyWithImpl<$Res>
    extends _$TherapyAnalyticsEventCopyWithImpl<$Res, _$LoadCustomRangeDataImpl>
    implements _$$LoadCustomRangeDataImplCopyWith<$Res> {
  __$$LoadCustomRangeDataImplCopyWithImpl(_$LoadCustomRangeDataImpl _value,
      $Res Function(_$LoadCustomRangeDataImpl) _then)
      : super(_value, _then);

  /// Create a copy of TherapyAnalyticsEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? startDate = null,
    Object? endDate = null,
  }) {
    return _then(_$LoadCustomRangeDataImpl(
      startDate: null == startDate
          ? _value.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      endDate: null == endDate
          ? _value.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc

class _$LoadCustomRangeDataImpl implements _LoadCustomRangeData {
  const _$LoadCustomRangeDataImpl(
      {required this.startDate, required this.endDate});

  @override
  final DateTime startDate;
  @override
  final DateTime endDate;

  @override
  String toString() {
    return 'TherapyAnalyticsEvent.loadCustomRangeData(startDate: $startDate, endDate: $endDate)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoadCustomRangeDataImpl &&
            (identical(other.startDate, startDate) ||
                other.startDate == startDate) &&
            (identical(other.endDate, endDate) || other.endDate == endDate));
  }

  @override
  int get hashCode => Object.hash(runtimeType, startDate, endDate);

  /// Create a copy of TherapyAnalyticsEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LoadCustomRangeDataImplCopyWith<_$LoadCustomRangeDataImpl> get copyWith =>
      __$$LoadCustomRangeDataImplCopyWithImpl<_$LoadCustomRangeDataImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadWeeklyData,
    required TResult Function() loadMonthlyData,
    required TResult Function(DateTime startDate, DateTime endDate)
        loadCustomRangeData,
    required TResult Function() refreshData,
  }) {
    return loadCustomRangeData(startDate, endDate);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadWeeklyData,
    TResult? Function()? loadMonthlyData,
    TResult? Function(DateTime startDate, DateTime endDate)?
        loadCustomRangeData,
    TResult? Function()? refreshData,
  }) {
    return loadCustomRangeData?.call(startDate, endDate);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadWeeklyData,
    TResult Function()? loadMonthlyData,
    TResult Function(DateTime startDate, DateTime endDate)? loadCustomRangeData,
    TResult Function()? refreshData,
    required TResult orElse(),
  }) {
    if (loadCustomRangeData != null) {
      return loadCustomRangeData(startDate, endDate);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadWeeklyData value) loadWeeklyData,
    required TResult Function(_LoadMonthlyData value) loadMonthlyData,
    required TResult Function(_LoadCustomRangeData value) loadCustomRangeData,
    required TResult Function(_RefreshData value) refreshData,
  }) {
    return loadCustomRangeData(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadWeeklyData value)? loadWeeklyData,
    TResult? Function(_LoadMonthlyData value)? loadMonthlyData,
    TResult? Function(_LoadCustomRangeData value)? loadCustomRangeData,
    TResult? Function(_RefreshData value)? refreshData,
  }) {
    return loadCustomRangeData?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadWeeklyData value)? loadWeeklyData,
    TResult Function(_LoadMonthlyData value)? loadMonthlyData,
    TResult Function(_LoadCustomRangeData value)? loadCustomRangeData,
    TResult Function(_RefreshData value)? refreshData,
    required TResult orElse(),
  }) {
    if (loadCustomRangeData != null) {
      return loadCustomRangeData(this);
    }
    return orElse();
  }
}

abstract class _LoadCustomRangeData implements TherapyAnalyticsEvent {
  const factory _LoadCustomRangeData(
      {required final DateTime startDate,
      required final DateTime endDate}) = _$LoadCustomRangeDataImpl;

  DateTime get startDate;
  DateTime get endDate;

  /// Create a copy of TherapyAnalyticsEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LoadCustomRangeDataImplCopyWith<_$LoadCustomRangeDataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$RefreshDataImplCopyWith<$Res> {
  factory _$$RefreshDataImplCopyWith(
          _$RefreshDataImpl value, $Res Function(_$RefreshDataImpl) then) =
      __$$RefreshDataImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$RefreshDataImplCopyWithImpl<$Res>
    extends _$TherapyAnalyticsEventCopyWithImpl<$Res, _$RefreshDataImpl>
    implements _$$RefreshDataImplCopyWith<$Res> {
  __$$RefreshDataImplCopyWithImpl(
      _$RefreshDataImpl _value, $Res Function(_$RefreshDataImpl) _then)
      : super(_value, _then);

  /// Create a copy of TherapyAnalyticsEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$RefreshDataImpl implements _RefreshData {
  const _$RefreshDataImpl();

  @override
  String toString() {
    return 'TherapyAnalyticsEvent.refreshData()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$RefreshDataImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadWeeklyData,
    required TResult Function() loadMonthlyData,
    required TResult Function(DateTime startDate, DateTime endDate)
        loadCustomRangeData,
    required TResult Function() refreshData,
  }) {
    return refreshData();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadWeeklyData,
    TResult? Function()? loadMonthlyData,
    TResult? Function(DateTime startDate, DateTime endDate)?
        loadCustomRangeData,
    TResult? Function()? refreshData,
  }) {
    return refreshData?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadWeeklyData,
    TResult Function()? loadMonthlyData,
    TResult Function(DateTime startDate, DateTime endDate)? loadCustomRangeData,
    TResult Function()? refreshData,
    required TResult orElse(),
  }) {
    if (refreshData != null) {
      return refreshData();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadWeeklyData value) loadWeeklyData,
    required TResult Function(_LoadMonthlyData value) loadMonthlyData,
    required TResult Function(_LoadCustomRangeData value) loadCustomRangeData,
    required TResult Function(_RefreshData value) refreshData,
  }) {
    return refreshData(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadWeeklyData value)? loadWeeklyData,
    TResult? Function(_LoadMonthlyData value)? loadMonthlyData,
    TResult? Function(_LoadCustomRangeData value)? loadCustomRangeData,
    TResult? Function(_RefreshData value)? refreshData,
  }) {
    return refreshData?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadWeeklyData value)? loadWeeklyData,
    TResult Function(_LoadMonthlyData value)? loadMonthlyData,
    TResult Function(_LoadCustomRangeData value)? loadCustomRangeData,
    TResult Function(_RefreshData value)? refreshData,
    required TResult orElse(),
  }) {
    if (refreshData != null) {
      return refreshData(this);
    }
    return orElse();
  }
}

abstract class _RefreshData implements TherapyAnalyticsEvent {
  const factory _RefreshData() = _$RefreshDataImpl;
}
