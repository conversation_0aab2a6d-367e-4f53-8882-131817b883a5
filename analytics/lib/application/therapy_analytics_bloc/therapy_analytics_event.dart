import 'package:freezed_annotation/freezed_annotation.dart';

part 'therapy_analytics_event.freezed.dart';

@freezed
class TherapyAnalyticsEvent with _$TherapyAnalyticsEvent {
  const factory TherapyAnalyticsEvent.loadWeeklyData() = _LoadWeeklyData;
  const factory TherapyAnalyticsEvent.loadMonthlyData() = _LoadMonthlyData;
  const factory TherapyAnalyticsEvent.loadCustomRangeData({
    required DateTime startDate,
    required DateTime endDate,
  }) = _LoadCustomRangeData;
  const factory TherapyAnalyticsEvent.refreshData() = _RefreshData;
}
