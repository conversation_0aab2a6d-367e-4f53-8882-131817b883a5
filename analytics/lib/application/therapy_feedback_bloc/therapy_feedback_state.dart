part of 'therapy_feedback_bloc.dart';

@freezed
class TherapyFeedbackState with _$TherapyFeedbackState {
  const factory TherapyFeedbackState.initial() = _Initial;
  const factory TherapyFeedbackState.loading() = _Loading;
  const factory TherapyFeedbackState.sessionLoaded(
      TherapySessionModel session) = _SessionLoaded;
  const factory TherapyFeedbackState.submitting() = _Submitting;
  const factory TherapyFeedbackState.submitted() = _Submitted;
  const factory TherapyFeedbackState.error(String message) = _Error;
}
