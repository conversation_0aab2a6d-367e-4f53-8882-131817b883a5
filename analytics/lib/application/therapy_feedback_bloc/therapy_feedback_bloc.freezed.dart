// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'therapy_feedback_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$TherapyFeedbackEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String sessionId) loadSessionData,
    required TResult Function(String sessionId, String? feedbackText,
            int? painLevelBefore, int? painLevelAfter)
        submitFeedback,
    required TResult Function() resetState,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String sessionId)? loadSessionData,
    TResult? Function(String sessionId, String? feedbackText,
            int? painLevelBefore, int? painLevelAfter)?
        submitFeedback,
    TResult? Function()? resetState,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String sessionId)? loadSessionData,
    TResult Function(String sessionId, String? feedbackText,
            int? painLevelBefore, int? painLevelAfter)?
        submitFeedback,
    TResult Function()? resetState,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadSessionData value) loadSessionData,
    required TResult Function(_SubmitFeedback value) submitFeedback,
    required TResult Function(_ResetState value) resetState,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadSessionData value)? loadSessionData,
    TResult? Function(_SubmitFeedback value)? submitFeedback,
    TResult? Function(_ResetState value)? resetState,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadSessionData value)? loadSessionData,
    TResult Function(_SubmitFeedback value)? submitFeedback,
    TResult Function(_ResetState value)? resetState,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TherapyFeedbackEventCopyWith<$Res> {
  factory $TherapyFeedbackEventCopyWith(TherapyFeedbackEvent value,
          $Res Function(TherapyFeedbackEvent) then) =
      _$TherapyFeedbackEventCopyWithImpl<$Res, TherapyFeedbackEvent>;
}

/// @nodoc
class _$TherapyFeedbackEventCopyWithImpl<$Res,
        $Val extends TherapyFeedbackEvent>
    implements $TherapyFeedbackEventCopyWith<$Res> {
  _$TherapyFeedbackEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TherapyFeedbackEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$LoadSessionDataImplCopyWith<$Res> {
  factory _$$LoadSessionDataImplCopyWith(_$LoadSessionDataImpl value,
          $Res Function(_$LoadSessionDataImpl) then) =
      __$$LoadSessionDataImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String sessionId});
}

/// @nodoc
class __$$LoadSessionDataImplCopyWithImpl<$Res>
    extends _$TherapyFeedbackEventCopyWithImpl<$Res, _$LoadSessionDataImpl>
    implements _$$LoadSessionDataImplCopyWith<$Res> {
  __$$LoadSessionDataImplCopyWithImpl(
      _$LoadSessionDataImpl _value, $Res Function(_$LoadSessionDataImpl) _then)
      : super(_value, _then);

  /// Create a copy of TherapyFeedbackEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? sessionId = null,
  }) {
    return _then(_$LoadSessionDataImpl(
      null == sessionId
          ? _value.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$LoadSessionDataImpl implements _LoadSessionData {
  const _$LoadSessionDataImpl(this.sessionId);

  @override
  final String sessionId;

  @override
  String toString() {
    return 'TherapyFeedbackEvent.loadSessionData(sessionId: $sessionId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoadSessionDataImpl &&
            (identical(other.sessionId, sessionId) ||
                other.sessionId == sessionId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, sessionId);

  /// Create a copy of TherapyFeedbackEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LoadSessionDataImplCopyWith<_$LoadSessionDataImpl> get copyWith =>
      __$$LoadSessionDataImplCopyWithImpl<_$LoadSessionDataImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String sessionId) loadSessionData,
    required TResult Function(String sessionId, String? feedbackText,
            int? painLevelBefore, int? painLevelAfter)
        submitFeedback,
    required TResult Function() resetState,
  }) {
    return loadSessionData(sessionId);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String sessionId)? loadSessionData,
    TResult? Function(String sessionId, String? feedbackText,
            int? painLevelBefore, int? painLevelAfter)?
        submitFeedback,
    TResult? Function()? resetState,
  }) {
    return loadSessionData?.call(sessionId);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String sessionId)? loadSessionData,
    TResult Function(String sessionId, String? feedbackText,
            int? painLevelBefore, int? painLevelAfter)?
        submitFeedback,
    TResult Function()? resetState,
    required TResult orElse(),
  }) {
    if (loadSessionData != null) {
      return loadSessionData(sessionId);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadSessionData value) loadSessionData,
    required TResult Function(_SubmitFeedback value) submitFeedback,
    required TResult Function(_ResetState value) resetState,
  }) {
    return loadSessionData(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadSessionData value)? loadSessionData,
    TResult? Function(_SubmitFeedback value)? submitFeedback,
    TResult? Function(_ResetState value)? resetState,
  }) {
    return loadSessionData?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadSessionData value)? loadSessionData,
    TResult Function(_SubmitFeedback value)? submitFeedback,
    TResult Function(_ResetState value)? resetState,
    required TResult orElse(),
  }) {
    if (loadSessionData != null) {
      return loadSessionData(this);
    }
    return orElse();
  }
}

abstract class _LoadSessionData implements TherapyFeedbackEvent {
  const factory _LoadSessionData(final String sessionId) =
      _$LoadSessionDataImpl;

  String get sessionId;

  /// Create a copy of TherapyFeedbackEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LoadSessionDataImplCopyWith<_$LoadSessionDataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SubmitFeedbackImplCopyWith<$Res> {
  factory _$$SubmitFeedbackImplCopyWith(_$SubmitFeedbackImpl value,
          $Res Function(_$SubmitFeedbackImpl) then) =
      __$$SubmitFeedbackImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {String sessionId,
      String? feedbackText,
      int? painLevelBefore,
      int? painLevelAfter});
}

/// @nodoc
class __$$SubmitFeedbackImplCopyWithImpl<$Res>
    extends _$TherapyFeedbackEventCopyWithImpl<$Res, _$SubmitFeedbackImpl>
    implements _$$SubmitFeedbackImplCopyWith<$Res> {
  __$$SubmitFeedbackImplCopyWithImpl(
      _$SubmitFeedbackImpl _value, $Res Function(_$SubmitFeedbackImpl) _then)
      : super(_value, _then);

  /// Create a copy of TherapyFeedbackEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? sessionId = null,
    Object? feedbackText = freezed,
    Object? painLevelBefore = freezed,
    Object? painLevelAfter = freezed,
  }) {
    return _then(_$SubmitFeedbackImpl(
      sessionId: null == sessionId
          ? _value.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String,
      feedbackText: freezed == feedbackText
          ? _value.feedbackText
          : feedbackText // ignore: cast_nullable_to_non_nullable
              as String?,
      painLevelBefore: freezed == painLevelBefore
          ? _value.painLevelBefore
          : painLevelBefore // ignore: cast_nullable_to_non_nullable
              as int?,
      painLevelAfter: freezed == painLevelAfter
          ? _value.painLevelAfter
          : painLevelAfter // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc

class _$SubmitFeedbackImpl implements _SubmitFeedback {
  const _$SubmitFeedbackImpl(
      {required this.sessionId,
      this.feedbackText,
      this.painLevelBefore,
      this.painLevelAfter});

  @override
  final String sessionId;
  @override
  final String? feedbackText;
  @override
  final int? painLevelBefore;
  @override
  final int? painLevelAfter;

  @override
  String toString() {
    return 'TherapyFeedbackEvent.submitFeedback(sessionId: $sessionId, feedbackText: $feedbackText, painLevelBefore: $painLevelBefore, painLevelAfter: $painLevelAfter)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SubmitFeedbackImpl &&
            (identical(other.sessionId, sessionId) ||
                other.sessionId == sessionId) &&
            (identical(other.feedbackText, feedbackText) ||
                other.feedbackText == feedbackText) &&
            (identical(other.painLevelBefore, painLevelBefore) ||
                other.painLevelBefore == painLevelBefore) &&
            (identical(other.painLevelAfter, painLevelAfter) ||
                other.painLevelAfter == painLevelAfter));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, sessionId, feedbackText, painLevelBefore, painLevelAfter);

  /// Create a copy of TherapyFeedbackEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SubmitFeedbackImplCopyWith<_$SubmitFeedbackImpl> get copyWith =>
      __$$SubmitFeedbackImplCopyWithImpl<_$SubmitFeedbackImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String sessionId) loadSessionData,
    required TResult Function(String sessionId, String? feedbackText,
            int? painLevelBefore, int? painLevelAfter)
        submitFeedback,
    required TResult Function() resetState,
  }) {
    return submitFeedback(
        sessionId, feedbackText, painLevelBefore, painLevelAfter);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String sessionId)? loadSessionData,
    TResult? Function(String sessionId, String? feedbackText,
            int? painLevelBefore, int? painLevelAfter)?
        submitFeedback,
    TResult? Function()? resetState,
  }) {
    return submitFeedback?.call(
        sessionId, feedbackText, painLevelBefore, painLevelAfter);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String sessionId)? loadSessionData,
    TResult Function(String sessionId, String? feedbackText,
            int? painLevelBefore, int? painLevelAfter)?
        submitFeedback,
    TResult Function()? resetState,
    required TResult orElse(),
  }) {
    if (submitFeedback != null) {
      return submitFeedback(
          sessionId, feedbackText, painLevelBefore, painLevelAfter);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadSessionData value) loadSessionData,
    required TResult Function(_SubmitFeedback value) submitFeedback,
    required TResult Function(_ResetState value) resetState,
  }) {
    return submitFeedback(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadSessionData value)? loadSessionData,
    TResult? Function(_SubmitFeedback value)? submitFeedback,
    TResult? Function(_ResetState value)? resetState,
  }) {
    return submitFeedback?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadSessionData value)? loadSessionData,
    TResult Function(_SubmitFeedback value)? submitFeedback,
    TResult Function(_ResetState value)? resetState,
    required TResult orElse(),
  }) {
    if (submitFeedback != null) {
      return submitFeedback(this);
    }
    return orElse();
  }
}

abstract class _SubmitFeedback implements TherapyFeedbackEvent {
  const factory _SubmitFeedback(
      {required final String sessionId,
      final String? feedbackText,
      final int? painLevelBefore,
      final int? painLevelAfter}) = _$SubmitFeedbackImpl;

  String get sessionId;
  String? get feedbackText;
  int? get painLevelBefore;
  int? get painLevelAfter;

  /// Create a copy of TherapyFeedbackEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SubmitFeedbackImplCopyWith<_$SubmitFeedbackImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ResetStateImplCopyWith<$Res> {
  factory _$$ResetStateImplCopyWith(
          _$ResetStateImpl value, $Res Function(_$ResetStateImpl) then) =
      __$$ResetStateImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ResetStateImplCopyWithImpl<$Res>
    extends _$TherapyFeedbackEventCopyWithImpl<$Res, _$ResetStateImpl>
    implements _$$ResetStateImplCopyWith<$Res> {
  __$$ResetStateImplCopyWithImpl(
      _$ResetStateImpl _value, $Res Function(_$ResetStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of TherapyFeedbackEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$ResetStateImpl implements _ResetState {
  const _$ResetStateImpl();

  @override
  String toString() {
    return 'TherapyFeedbackEvent.resetState()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$ResetStateImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String sessionId) loadSessionData,
    required TResult Function(String sessionId, String? feedbackText,
            int? painLevelBefore, int? painLevelAfter)
        submitFeedback,
    required TResult Function() resetState,
  }) {
    return resetState();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String sessionId)? loadSessionData,
    TResult? Function(String sessionId, String? feedbackText,
            int? painLevelBefore, int? painLevelAfter)?
        submitFeedback,
    TResult? Function()? resetState,
  }) {
    return resetState?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String sessionId)? loadSessionData,
    TResult Function(String sessionId, String? feedbackText,
            int? painLevelBefore, int? painLevelAfter)?
        submitFeedback,
    TResult Function()? resetState,
    required TResult orElse(),
  }) {
    if (resetState != null) {
      return resetState();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadSessionData value) loadSessionData,
    required TResult Function(_SubmitFeedback value) submitFeedback,
    required TResult Function(_ResetState value) resetState,
  }) {
    return resetState(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadSessionData value)? loadSessionData,
    TResult? Function(_SubmitFeedback value)? submitFeedback,
    TResult? Function(_ResetState value)? resetState,
  }) {
    return resetState?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadSessionData value)? loadSessionData,
    TResult Function(_SubmitFeedback value)? submitFeedback,
    TResult Function(_ResetState value)? resetState,
    required TResult orElse(),
  }) {
    if (resetState != null) {
      return resetState(this);
    }
    return orElse();
  }
}

abstract class _ResetState implements TherapyFeedbackEvent {
  const factory _ResetState() = _$ResetStateImpl;
}

/// @nodoc
mixin _$TherapyFeedbackState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(TherapySessionModel session) sessionLoaded,
    required TResult Function() submitting,
    required TResult Function() submitted,
    required TResult Function(String message) error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(TherapySessionModel session)? sessionLoaded,
    TResult? Function()? submitting,
    TResult? Function()? submitted,
    TResult? Function(String message)? error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(TherapySessionModel session)? sessionLoaded,
    TResult Function()? submitting,
    TResult Function()? submitted,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_SessionLoaded value) sessionLoaded,
    required TResult Function(_Submitting value) submitting,
    required TResult Function(_Submitted value) submitted,
    required TResult Function(_Error value) error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_SessionLoaded value)? sessionLoaded,
    TResult? Function(_Submitting value)? submitting,
    TResult? Function(_Submitted value)? submitted,
    TResult? Function(_Error value)? error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_SessionLoaded value)? sessionLoaded,
    TResult Function(_Submitting value)? submitting,
    TResult Function(_Submitted value)? submitted,
    TResult Function(_Error value)? error,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TherapyFeedbackStateCopyWith<$Res> {
  factory $TherapyFeedbackStateCopyWith(TherapyFeedbackState value,
          $Res Function(TherapyFeedbackState) then) =
      _$TherapyFeedbackStateCopyWithImpl<$Res, TherapyFeedbackState>;
}

/// @nodoc
class _$TherapyFeedbackStateCopyWithImpl<$Res,
        $Val extends TherapyFeedbackState>
    implements $TherapyFeedbackStateCopyWith<$Res> {
  _$TherapyFeedbackStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TherapyFeedbackState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$InitialImplCopyWith<$Res> {
  factory _$$InitialImplCopyWith(
          _$InitialImpl value, $Res Function(_$InitialImpl) then) =
      __$$InitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InitialImplCopyWithImpl<$Res>
    extends _$TherapyFeedbackStateCopyWithImpl<$Res, _$InitialImpl>
    implements _$$InitialImplCopyWith<$Res> {
  __$$InitialImplCopyWithImpl(
      _$InitialImpl _value, $Res Function(_$InitialImpl) _then)
      : super(_value, _then);

  /// Create a copy of TherapyFeedbackState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$InitialImpl implements _Initial {
  const _$InitialImpl();

  @override
  String toString() {
    return 'TherapyFeedbackState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$InitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(TherapySessionModel session) sessionLoaded,
    required TResult Function() submitting,
    required TResult Function() submitted,
    required TResult Function(String message) error,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(TherapySessionModel session)? sessionLoaded,
    TResult? Function()? submitting,
    TResult? Function()? submitted,
    TResult? Function(String message)? error,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(TherapySessionModel session)? sessionLoaded,
    TResult Function()? submitting,
    TResult Function()? submitted,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_SessionLoaded value) sessionLoaded,
    required TResult Function(_Submitting value) submitting,
    required TResult Function(_Submitted value) submitted,
    required TResult Function(_Error value) error,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_SessionLoaded value)? sessionLoaded,
    TResult? Function(_Submitting value)? submitting,
    TResult? Function(_Submitted value)? submitted,
    TResult? Function(_Error value)? error,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_SessionLoaded value)? sessionLoaded,
    TResult Function(_Submitting value)? submitting,
    TResult Function(_Submitted value)? submitted,
    TResult Function(_Error value)? error,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class _Initial implements TherapyFeedbackState {
  const factory _Initial() = _$InitialImpl;
}

/// @nodoc
abstract class _$$LoadingImplCopyWith<$Res> {
  factory _$$LoadingImplCopyWith(
          _$LoadingImpl value, $Res Function(_$LoadingImpl) then) =
      __$$LoadingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadingImplCopyWithImpl<$Res>
    extends _$TherapyFeedbackStateCopyWithImpl<$Res, _$LoadingImpl>
    implements _$$LoadingImplCopyWith<$Res> {
  __$$LoadingImplCopyWithImpl(
      _$LoadingImpl _value, $Res Function(_$LoadingImpl) _then)
      : super(_value, _then);

  /// Create a copy of TherapyFeedbackState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadingImpl implements _Loading {
  const _$LoadingImpl();

  @override
  String toString() {
    return 'TherapyFeedbackState.loading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoadingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(TherapySessionModel session) sessionLoaded,
    required TResult Function() submitting,
    required TResult Function() submitted,
    required TResult Function(String message) error,
  }) {
    return loading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(TherapySessionModel session)? sessionLoaded,
    TResult? Function()? submitting,
    TResult? Function()? submitted,
    TResult? Function(String message)? error,
  }) {
    return loading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(TherapySessionModel session)? sessionLoaded,
    TResult Function()? submitting,
    TResult Function()? submitted,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_SessionLoaded value) sessionLoaded,
    required TResult Function(_Submitting value) submitting,
    required TResult Function(_Submitted value) submitted,
    required TResult Function(_Error value) error,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_SessionLoaded value)? sessionLoaded,
    TResult? Function(_Submitting value)? submitting,
    TResult? Function(_Submitted value)? submitted,
    TResult? Function(_Error value)? error,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_SessionLoaded value)? sessionLoaded,
    TResult Function(_Submitting value)? submitting,
    TResult Function(_Submitted value)? submitted,
    TResult Function(_Error value)? error,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class _Loading implements TherapyFeedbackState {
  const factory _Loading() = _$LoadingImpl;
}

/// @nodoc
abstract class _$$SessionLoadedImplCopyWith<$Res> {
  factory _$$SessionLoadedImplCopyWith(
          _$SessionLoadedImpl value, $Res Function(_$SessionLoadedImpl) then) =
      __$$SessionLoadedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({TherapySessionModel session});
}

/// @nodoc
class __$$SessionLoadedImplCopyWithImpl<$Res>
    extends _$TherapyFeedbackStateCopyWithImpl<$Res, _$SessionLoadedImpl>
    implements _$$SessionLoadedImplCopyWith<$Res> {
  __$$SessionLoadedImplCopyWithImpl(
      _$SessionLoadedImpl _value, $Res Function(_$SessionLoadedImpl) _then)
      : super(_value, _then);

  /// Create a copy of TherapyFeedbackState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? session = null,
  }) {
    return _then(_$SessionLoadedImpl(
      null == session
          ? _value.session
          : session // ignore: cast_nullable_to_non_nullable
              as TherapySessionModel,
    ));
  }
}

/// @nodoc

class _$SessionLoadedImpl implements _SessionLoaded {
  const _$SessionLoadedImpl(this.session);

  @override
  final TherapySessionModel session;

  @override
  String toString() {
    return 'TherapyFeedbackState.sessionLoaded(session: $session)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SessionLoadedImpl &&
            (identical(other.session, session) || other.session == session));
  }

  @override
  int get hashCode => Object.hash(runtimeType, session);

  /// Create a copy of TherapyFeedbackState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SessionLoadedImplCopyWith<_$SessionLoadedImpl> get copyWith =>
      __$$SessionLoadedImplCopyWithImpl<_$SessionLoadedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(TherapySessionModel session) sessionLoaded,
    required TResult Function() submitting,
    required TResult Function() submitted,
    required TResult Function(String message) error,
  }) {
    return sessionLoaded(session);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(TherapySessionModel session)? sessionLoaded,
    TResult? Function()? submitting,
    TResult? Function()? submitted,
    TResult? Function(String message)? error,
  }) {
    return sessionLoaded?.call(session);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(TherapySessionModel session)? sessionLoaded,
    TResult Function()? submitting,
    TResult Function()? submitted,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (sessionLoaded != null) {
      return sessionLoaded(session);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_SessionLoaded value) sessionLoaded,
    required TResult Function(_Submitting value) submitting,
    required TResult Function(_Submitted value) submitted,
    required TResult Function(_Error value) error,
  }) {
    return sessionLoaded(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_SessionLoaded value)? sessionLoaded,
    TResult? Function(_Submitting value)? submitting,
    TResult? Function(_Submitted value)? submitted,
    TResult? Function(_Error value)? error,
  }) {
    return sessionLoaded?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_SessionLoaded value)? sessionLoaded,
    TResult Function(_Submitting value)? submitting,
    TResult Function(_Submitted value)? submitted,
    TResult Function(_Error value)? error,
    required TResult orElse(),
  }) {
    if (sessionLoaded != null) {
      return sessionLoaded(this);
    }
    return orElse();
  }
}

abstract class _SessionLoaded implements TherapyFeedbackState {
  const factory _SessionLoaded(final TherapySessionModel session) =
      _$SessionLoadedImpl;

  TherapySessionModel get session;

  /// Create a copy of TherapyFeedbackState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SessionLoadedImplCopyWith<_$SessionLoadedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SubmittingImplCopyWith<$Res> {
  factory _$$SubmittingImplCopyWith(
          _$SubmittingImpl value, $Res Function(_$SubmittingImpl) then) =
      __$$SubmittingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$SubmittingImplCopyWithImpl<$Res>
    extends _$TherapyFeedbackStateCopyWithImpl<$Res, _$SubmittingImpl>
    implements _$$SubmittingImplCopyWith<$Res> {
  __$$SubmittingImplCopyWithImpl(
      _$SubmittingImpl _value, $Res Function(_$SubmittingImpl) _then)
      : super(_value, _then);

  /// Create a copy of TherapyFeedbackState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$SubmittingImpl implements _Submitting {
  const _$SubmittingImpl();

  @override
  String toString() {
    return 'TherapyFeedbackState.submitting()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$SubmittingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(TherapySessionModel session) sessionLoaded,
    required TResult Function() submitting,
    required TResult Function() submitted,
    required TResult Function(String message) error,
  }) {
    return submitting();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(TherapySessionModel session)? sessionLoaded,
    TResult? Function()? submitting,
    TResult? Function()? submitted,
    TResult? Function(String message)? error,
  }) {
    return submitting?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(TherapySessionModel session)? sessionLoaded,
    TResult Function()? submitting,
    TResult Function()? submitted,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (submitting != null) {
      return submitting();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_SessionLoaded value) sessionLoaded,
    required TResult Function(_Submitting value) submitting,
    required TResult Function(_Submitted value) submitted,
    required TResult Function(_Error value) error,
  }) {
    return submitting(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_SessionLoaded value)? sessionLoaded,
    TResult? Function(_Submitting value)? submitting,
    TResult? Function(_Submitted value)? submitted,
    TResult? Function(_Error value)? error,
  }) {
    return submitting?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_SessionLoaded value)? sessionLoaded,
    TResult Function(_Submitting value)? submitting,
    TResult Function(_Submitted value)? submitted,
    TResult Function(_Error value)? error,
    required TResult orElse(),
  }) {
    if (submitting != null) {
      return submitting(this);
    }
    return orElse();
  }
}

abstract class _Submitting implements TherapyFeedbackState {
  const factory _Submitting() = _$SubmittingImpl;
}

/// @nodoc
abstract class _$$SubmittedImplCopyWith<$Res> {
  factory _$$SubmittedImplCopyWith(
          _$SubmittedImpl value, $Res Function(_$SubmittedImpl) then) =
      __$$SubmittedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$SubmittedImplCopyWithImpl<$Res>
    extends _$TherapyFeedbackStateCopyWithImpl<$Res, _$SubmittedImpl>
    implements _$$SubmittedImplCopyWith<$Res> {
  __$$SubmittedImplCopyWithImpl(
      _$SubmittedImpl _value, $Res Function(_$SubmittedImpl) _then)
      : super(_value, _then);

  /// Create a copy of TherapyFeedbackState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$SubmittedImpl implements _Submitted {
  const _$SubmittedImpl();

  @override
  String toString() {
    return 'TherapyFeedbackState.submitted()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$SubmittedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(TherapySessionModel session) sessionLoaded,
    required TResult Function() submitting,
    required TResult Function() submitted,
    required TResult Function(String message) error,
  }) {
    return submitted();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(TherapySessionModel session)? sessionLoaded,
    TResult? Function()? submitting,
    TResult? Function()? submitted,
    TResult? Function(String message)? error,
  }) {
    return submitted?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(TherapySessionModel session)? sessionLoaded,
    TResult Function()? submitting,
    TResult Function()? submitted,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (submitted != null) {
      return submitted();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_SessionLoaded value) sessionLoaded,
    required TResult Function(_Submitting value) submitting,
    required TResult Function(_Submitted value) submitted,
    required TResult Function(_Error value) error,
  }) {
    return submitted(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_SessionLoaded value)? sessionLoaded,
    TResult? Function(_Submitting value)? submitting,
    TResult? Function(_Submitted value)? submitted,
    TResult? Function(_Error value)? error,
  }) {
    return submitted?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_SessionLoaded value)? sessionLoaded,
    TResult Function(_Submitting value)? submitting,
    TResult Function(_Submitted value)? submitted,
    TResult Function(_Error value)? error,
    required TResult orElse(),
  }) {
    if (submitted != null) {
      return submitted(this);
    }
    return orElse();
  }
}

abstract class _Submitted implements TherapyFeedbackState {
  const factory _Submitted() = _$SubmittedImpl;
}

/// @nodoc
abstract class _$$ErrorImplCopyWith<$Res> {
  factory _$$ErrorImplCopyWith(
          _$ErrorImpl value, $Res Function(_$ErrorImpl) then) =
      __$$ErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$ErrorImplCopyWithImpl<$Res>
    extends _$TherapyFeedbackStateCopyWithImpl<$Res, _$ErrorImpl>
    implements _$$ErrorImplCopyWith<$Res> {
  __$$ErrorImplCopyWithImpl(
      _$ErrorImpl _value, $Res Function(_$ErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of TherapyFeedbackState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$ErrorImpl(
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$ErrorImpl implements _Error {
  const _$ErrorImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'TherapyFeedbackState.error(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ErrorImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of TherapyFeedbackState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ErrorImplCopyWith<_$ErrorImpl> get copyWith =>
      __$$ErrorImplCopyWithImpl<_$ErrorImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(TherapySessionModel session) sessionLoaded,
    required TResult Function() submitting,
    required TResult Function() submitted,
    required TResult Function(String message) error,
  }) {
    return error(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(TherapySessionModel session)? sessionLoaded,
    TResult? Function()? submitting,
    TResult? Function()? submitted,
    TResult? Function(String message)? error,
  }) {
    return error?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(TherapySessionModel session)? sessionLoaded,
    TResult Function()? submitting,
    TResult Function()? submitted,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_SessionLoaded value) sessionLoaded,
    required TResult Function(_Submitting value) submitting,
    required TResult Function(_Submitted value) submitted,
    required TResult Function(_Error value) error,
  }) {
    return error(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_SessionLoaded value)? sessionLoaded,
    TResult? Function(_Submitting value)? submitting,
    TResult? Function(_Submitted value)? submitted,
    TResult? Function(_Error value)? error,
  }) {
    return error?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_SessionLoaded value)? sessionLoaded,
    TResult Function(_Submitting value)? submitting,
    TResult Function(_Submitted value)? submitted,
    TResult Function(_Error value)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(this);
    }
    return orElse();
  }
}

abstract class _Error implements TherapyFeedbackState {
  const factory _Error(final String message) = _$ErrorImpl;

  String get message;

  /// Create a copy of TherapyFeedbackState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ErrorImplCopyWith<_$ErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
