import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:fpdart/fpdart.dart';
import 'package:analytics/analytics.dart';

class MockAnalyticsFacade extends Mock implements IAnalyticsFacade {}

void main() {
  group('TherapyAnalyticsBloc', () {
    late TherapyAnalyticsBloc bloc;
    late MockAnalyticsFacade mockAnalyticsFacade;

    setUp(() {
      mockAnalyticsFacade = MockAnalyticsFacade();
      bloc = TherapyAnalyticsBloc(mockAnalyticsFacade);
    });

    tearDown(() {
      bloc.close();
    });

    test('initial state is correct', () {
      expect(bloc.state, const TherapyAnalyticsState.initial());
    });

    test('emits loading then loaded when loadWeeklyData succeeds', () async {
      // Arrange
      final mockSessions = <TherapySessionModel>[];
      when(mockAnalyticsFacade.getTherapySessionsLocal())
          .thenAnswer((_) async => Right(mockSessions));

      // Act
      bloc.add(const TherapyAnalyticsEvent.loadWeeklyData());

      // Assert
      await expectLater(
        bloc.stream,
        emitsInOrder([
          const TherapyAnalyticsState.loading(),
          isA<TherapyAnalyticsState>().having(
            (state) => state.maybeWhen(
              loaded: (_, __, ___, ____) => true,
              orElse: () => false,
            ),
            'is loaded state',
            true,
          ),
        ]),
      );
    });

    test('emits loading then error when loadWeeklyData fails', () async {
      // Arrange
      const errorMessage = 'Failed to load sessions';
      when(mockAnalyticsFacade.getTherapySessionsLocal())
          .thenAnswer((_) async => const Left(errorMessage));

      // Act
      bloc.add(const TherapyAnalyticsEvent.loadWeeklyData());

      // Assert
      await expectLater(
        bloc.stream,
        emitsInOrder([
          const TherapyAnalyticsState.loading(),
          const TherapyAnalyticsState.error(message: errorMessage),
        ]),
      );
    });
  });
}
