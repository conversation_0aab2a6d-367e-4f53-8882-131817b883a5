import 'dart:async';

import 'package:bluetooth/domain/facade/bluetooth_facade.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';

/// Manages Bluetooth device connection monitoring
///
/// Handles device connection and disconnection events
class ConnectionManager {
  final IBluetoothFacade _bluetoothFacade;

  // Connection monitoring
  StreamSubscription? _deviceConnectionSubscription;
  StreamSubscription? _deviceInfoSubscription;

  // Callbacks for connection events
  final Function() _onDeviceConnected;
  final Function() _onDeviceDisconnected;

  // Track connected device to prevent duplicate notifications
  String? _connectedDeviceId;
  bool _isSettingUpConnection = false;
  bool _isDisconnected = false;

  ConnectionManager({
    required IBluetoothFacade bluetoothFacade,
    required Function() onDeviceConnected,
    required Function() onDeviceDisconnected,
  })  : _bluetoothFacade = bluetoothFacade,
        _onDeviceConnected = onDeviceConnected,
        _onDeviceDisconnected = onDeviceDisconnected {
    _initConnectionMonitoring();
  }

  /// Initialize device connection monitoring
  Future<void> _initConnectionMonitoring() async {
    debugPrint("Initializing device connection monitoring");

    // Get the currently connected device
    final deviceResult = await _bluetoothFacade.getConnectedDevice();

    deviceResult.mapBoth(onLeft: (failure) {
      // No device connected or error, nothing to monitor yet
      debugPrint('No device connected to monitor: $failure');
    }, onRight: (device) {
      // Start monitoring this device's connection state
      _setupConnectionMonitoring(device);
    });

    // Also listen to device information changes to detect new connections
    _deviceInfoSubscription?.cancel();
    _deviceInfoSubscription =
        _bluetoothFacade.getDeviceInformation().listen((deviceInfo) {
      deviceInfo.mapBoth(onLeft: (failure) {
        // Error handling
      }, onRight: (deviceModel) async {
        // Check if this is a different device than we already know about
        // AND we're not currently disconnected (to prevent immediate reconnection loop)
        if (deviceModel.deviceInfo.deviceId != _connectedDeviceId &&
            !_isSettingUpConnection &&
            !_isDisconnected) {
          // When device info changes, check if we have a connected device
          final deviceResult = await _bluetoothFacade.getConnectedDevice();
          deviceResult.mapBoth(onLeft: (failure) {
            // No device connected
          }, onRight: (device) {
            _setupConnectionMonitoring(device);
          });
        }
      });
    });
  }

  /// Set up monitoring for a specific device
  void _setupConnectionMonitoring(BluetoothDevice device) {
    if (_connectedDeviceId == device.remoteId.str) {
      // Already monitoring this device
      return;
    }

    _isSettingUpConnection = true;

    try {
      // Cancel any existing subscription
      _deviceConnectionSubscription?.cancel();

      // Store the connected device ID
      _connectedDeviceId = device.remoteId.str;
      debugPrint("Setting up monitoring for device: ${device.remoteId.str}");

      // Monitor connection state changes
      _deviceConnectionSubscription =
          _bluetoothFacade.listenToDevice(device).listen((connectionState) {
        if (connectionState == BluetoothConnectionState.disconnected) {
          debugPrint("Device disconnected: ${device.remoteId}");
          _connectedDeviceId = null;
          _isDisconnected = true;

          _onDeviceDisconnected();
        } else if (connectionState == BluetoothConnectionState.connected) {
          debugPrint("Device connected: ${device.remoteId}");
          _isDisconnected = false;
          // Only trigger once per connection
          if (_connectedDeviceId == device.remoteId.str) {
            _onDeviceConnected();
          }
        }
      });
    } finally {
      _isSettingUpConnection = false;
    }
  }

  /// Check if a device is currently connected
  bool get isDeviceConnected => _connectedDeviceId != null;

  /// Get the ID of the connected device
  String? get connectedDeviceId => _connectedDeviceId;

  /// Allow reconnection attempts after disconnection
  void allowReconnection() {
    _isDisconnected = false;
  }

  /// Clean up resources
  void dispose() {
    _deviceConnectionSubscription?.cancel();
    _deviceConnectionSubscription = null;
    _deviceInfoSubscription?.cancel();
    _deviceInfoSubscription = null;
    _connectedDeviceId = null;
  }
}
