import 'dart:async';

import 'package:flutter/foundation.dart';

/// Manages adaptive polling for device data updates
///
/// Responsible for scheduling and executing polls at appropriate intervals
/// based on user activity and device state
class PollingManager {
  // Mutex locks for preventing concurrent operations
  bool _isHeatPolling = false;
  bool _isTensPolling = false;
  bool _isDeviceStatusPolling = false;

  // User activity timestamps
  DateTime _lastHeatActivity = DateTime.now();
  DateTime _lastTensActivity = DateTime.now();

  // Single timer for all polling operations
  Timer? _pollingTimer;

  // Polling intervals
  static const Duration _normalPollInterval = Duration(seconds: 15);
  static const Duration _rapidPollInterval = Duration(seconds: 5);
  static const Duration _activityThreshold = Duration(seconds: 30);
  static const Duration _afterCommandReadDelay = Duration(milliseconds: 500);

  // Polling callbacks
  final Function() _onPollHeat;
  final Function() _onPollTens;
  final Function() _onPollDeviceStatus;

  PollingManager({
    required Function() onPollHeat,
    required Function() onPollTens,
    required Function() onPollDeviceStatus,
  })  : _onPollHeat = onPollHeat,
        _onPollTens = onPollTens,
        _onPollDeviceStatus = onPollDeviceStatus {
    _scheduleNextPoll();
  }

  /// Start adaptive polling based on user activity
  void _scheduleNextPoll() {
    // Cancel existing timer
    _pollingTimer?.cancel();

    // Determine appropriate polling interval based on user activity
    final now = DateTime.now();
    final sinceLastHeatActivity = now.difference(_lastHeatActivity);
    final sinceLastTensActivity = now.difference(_lastTensActivity);

    // Use rapid polling if there was recent activity
    final bool isRecentActivity = sinceLastHeatActivity < _activityThreshold ||
        sinceLastTensActivity < _activityThreshold;

    final interval =
        isRecentActivity ? _rapidPollInterval : _normalPollInterval;

    // Create new timer with the calculated interval
    _pollingTimer = Timer(interval, () {
      _executePoll(isRapidPolling: isRecentActivity);
    });
  }

  /// Execute the scheduled poll operations
  Future<void> _executePoll({bool isRapidPolling = false}) async {
    try {
      // Run appropriate polls based on activity and need
      if (!_isHeatPolling) {
        _isHeatPolling = true;
        try {
          _onPollHeat();
        } finally {
          _isHeatPolling = false;
        }
      }

      if (!_isTensPolling) {
        _isTensPolling = true;
        try {
          _onPollTens();
        } finally {
          _isTensPolling = false;
        }
      }

      // Only check device status during normal polling to save resources
      if (!isRapidPolling && !_isDeviceStatusPolling) {
        _isDeviceStatusPolling = true;
        try {
          _onPollDeviceStatus();
        } finally {
          _isDeviceStatusPolling = false;
        }
      }
    } catch (e) {
      debugPrint('Error during polling: $e');
    } finally {
      // Schedule next poll regardless of success/failure
      _scheduleNextPoll();
    }
  }

  /// Record heat-related user activity to trigger rapid polling
  void markHeatActivity() {
    _lastHeatActivity = DateTime.now();
    // Reschedule polling immediately when there's user activity
    _scheduleNextPoll();
  }

  /// Record TENS-related user activity to trigger rapid polling
  void markTensActivity() {
    _lastTensActivity = DateTime.now();
    // Reschedule polling immediately when there's user activity
    _scheduleNextPoll();
  }

  /// Request an immediate poll for all features
  void requestImmediatePoll() {
    _pollingTimer?.cancel();
    _executePoll(isRapidPolling: true);
  }

  /// Stop all polling activities immediately
  ///
  /// This should be called when a device is disconnected to prevent
  /// unnecessary polling attempts to an unavailable device
  void stopAllPolling() {
    debugPrint('Stopping all polling activities');
    _pollingTimer?.cancel();
    _pollingTimer = null;

    // Reset polling flags to ensure clean state
    _isHeatPolling = false;
    _isTensPolling = false;
    _isDeviceStatusPolling = false;
  }

  /// Clean up resources
  void dispose() {
    _pollingTimer?.cancel();
    _pollingTimer = null;
  }
}
