import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:fpdart/fpdart.dart';
import 'package:remote/domain/model/commands_model.dart';
import 'package:remote/domain/model/device_model.dart';
import 'package:remote/domain/model/heat_level.dart';

import '../failure/bluetooth_failure.dart';

/// IBluetoothFacade is an abstract class that defines the contract for the Bluetooth service.
///It includes methods for checking Bluetooth status, getting paired devices, searching for devices,
/// listening to a device, pairing and unpairing a device, checking if a device is connected,
/// saving a device, getting a saved device, and connecting to saved devices.
abstract class IBluetoothFacade {
  // Method to check if Bluetooth is on
  Stream<bool> isBluetoothOn();

  // Method to get paired devices
  Future<Either<BluetoothFailure, List<BluetoothDevice>>> getPairedDevices();

  // Method to search for devices
  Future<Either<BluetoothFailure, List<BluetoothDevice?>?>> searchForDevices();

  // Method to listen to a device
  Stream<BluetoothConnectionState> listenToDevice(BluetoothDevice device);

  // Method to pair a device
  Future<Either<BluetoothFailure, BluetoothDevice>> pairDevice(
      String device, bool initial);

  // Method to unpair a device
  Future<Either<BluetoothFailure, void>> unpairDevice(BluetoothDevice device);

  // Method to check if a device is connected
  Future<Either<BluetoothFailure, BluetoothDevice?>> deviceConnected();

  // Method to save a device
  Future<Either<BluetoothFailure, bool>> saveDevice(BluetoothDevice device);

  // Method to get a saved devices
  Stream<Either<BluetoothFailure, List<DeviceModel>>> getSavedDevicesList();

  //get recent device
  Future<Either<BluetoothFailure, DeviceModel>> getRecentDevice();

  // Method to connect to saved devices
  // Future<Either<BluetoothFailure, BluetoothDevice>> connectToSavedDevices(String? devices);
  // Method to send command
  Future<Either<BluetoothFailure, Unit>> sendCommand(Commands command);
  // Method to get command
  Stream<Either<BluetoothFailure, DeviceModel>> getDeviceInformation();

  // Method to read device characteristics
  Future<Either<BluetoothFailure, List<int>>> readDeviceCharacteristics(
      Commands command);

  //method to run reconnect process
  Future<Either<BluetoothFailure, BluetoothDevice>> reconnectToDevice(
      BluetoothDevice device);

  // Method to stop scanning for devices
  Future<Either<BluetoothFailure, void>> stopScanning();

  // Method to stop background scanning for saved devices
  void stopBackgroundScanning();

  // get Connected device
  Future<Either<BluetoothFailure, BluetoothDevice>> getConnectedDevice();
  //listen to indication
  Stream<Either<BluetoothFailure, List<int>>> listenToIndication(
      Commands command);
}
