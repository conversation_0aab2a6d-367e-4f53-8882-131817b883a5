import 'dart:io';
import 'dart:math';
import 'package:bloc_test/bloc_test.dart';
import 'package:bluetooth/application/bluetooth_service_bloc/bluetooth_service_bloc.dart';
import 'package:bluetooth/domain/failure/bluetooth_failure.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:permission_handler/permission_handler.dart';
import '../repository/bluetooth_native_functions.dart';

// The BluetoothWrapperPage class is a StatefulWidget that manages the different
// states of the Bluetooth service. It displays different pages based on the current
// state of the Bluetooth service.
class BluetoothWrapperPage extends StatefulWidget {
  // The different pages to display based on the Bluetooth service state
  final Widget bluetoothOffPage;
  final Widget noDeviceFoundPage;
  final Widget bluetoothSearchingPage;
  final Widget bluetoothDeviceListPage;
  final Widget remotePage;
  final Widget deviceIntroPage;
  final Widget deviceReconnectingPage;
  final Widget landingPage;
  final Widget connectingPage;

  // Constructor for the BluetoothWrapperPage class
  const BluetoothWrapperPage({
    Key? key,
    required this.bluetoothOffPage,
    required this.noDeviceFoundPage,
    required this.bluetoothSearchingPage,
    required this.bluetoothDeviceListPage,
    required this.remotePage,
    required this.deviceIntroPage,
    required this.deviceReconnectingPage,
    required this.landingPage,
    required this.connectingPage,
  }) : super(key: key);

  @override
  BluetoothWrapperPageState createState() => BluetoothWrapperPageState();
}

// The BluetoothWrapperPageState class is the state object for the BluetoothWrapperPage widget.
// It manages the lifecycle of the Bluetooth service and updates the UI based on the current state.
class BluetoothWrapperPageState extends State<BluetoothWrapperPage> {
  @override
  void initState() {
    // Start monitoring the Bluetooth service when the widget is initialized
    if (Platform.isIOS) {
      BluetoothSettings.startBluetoothMonitoring();
    }
    super.initState();
  }

  @override
  void dispose() {
    // Stop monitoring the Bluetooth service when the widget is disposed
    if (Platform.isIOS) {
      BluetoothSettings.stopBluetoothMonitoring();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Build the UI based on the current state of the Bluetooth service
    return Scaffold(
      body: BlocConsumer<BluetoothServiceBloc, BluetoothServiceState>(
        listener: (context, state) {
          // Listener for state changes in the Bluetooth service
        },
        builder: (context, state) {
          // Builder for the UI based on the current state
          if (state is Initial) {
            return Container(
              color: Colors.white,
            );
          } else if (state is BluetoothOff) {
            return widget.bluetoothOffPage;
          } else if (state is BluetoothSearching) {
            return widget.bluetoothSearchingPage;
          } else if (state is Connecting) {
            return widget.connectingPage;
          }
           else if (state is BluetoothAvailableTypeDevices) {
            return state.devices!.isNotEmpty
                ? widget.bluetoothDeviceListPage
                : widget.noDeviceFoundPage;
            // : widget.noDeviceFoundPage;
          } else if (state is LandingPageState) {
            return widget.landingPage;
          }

           else if (state is SavedDevices) {
            print("Saved Devices: ${state.devices}");
            return widget.landingPage;
          } else if (state is BluetoothError) {
            print("Bluetooth Error: ${state.failure}");
            if (state.failure == BluetoothFailure.noDevicesFound()) {
              return widget.noDeviceFoundPage;
            }
          } else if (state is Connected) {
            return widget.remotePage;
          } else if (state is Reconnecting) {
            return widget.deviceReconnectingPage;
          } else if (state is ConnectionIntro) {
            return widget.deviceIntroPage;
          } else if (state is Disconnected) {
            return GestureDetector(
                onTap: () {
                  BlocProvider.of<BluetoothServiceBloc>(context)
                      .add(const CheckConnection());
                },
                child: Container(
                  color: Colors.red,
                ));
            // return widget.noDeviceFoundPage;
          }
          return widget.bluetoothOffPage; // Fallback to landing page
        },
      ),
    );
  }
}
